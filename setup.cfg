[metadata]
name = kubespray
summary = Ansible modules for installing Kubernetes
description-file =
    README.md
author = <PERSON><PERSON>pray
author-email = <EMAIL>
license = Apache License (2.0)
home-page = https://github.com/kubernetes-sigs/kubespray
classifier =
  License :: OSI Approved :: Apache Software License
  Development Status :: 4 - Beta
  Intended Audience :: Developers
  Intended Audience :: System Administrators
  Intended Audience :: Information Technology
  Topic :: Utilities

[global]
setup-hooks =
    pbr.hooks.setup_hook

[files]
data_files =
    usr/share/kubespray/playbooks/ =
        cluster.yml
        upgrade-cluster.yml
        scale.yml
        reset.yml
        remove-node.yml
        extra_playbooks/upgrade-only-k8s.yml
    usr/share/kubespray/roles = roles/*
    usr/share/kubespray/library = library/*
    usr/share/doc/kubespray/ =
        LICENSE
        README.md
    usr/share/doc/kubespray/inventory/ =
        inventory/sample/inventory.ini
    etc/kubespray/ =
        ansible.cfg
    etc/kubespray/inventory/sample/group_vars/ =
        inventory/sample/group_vars/etcd.yml
    etc/kubespray/inventory/sample/group_vars/all/ =
        inventory/sample/group_vars/all/all.yml
        inventory/sample/group_vars/all/azure.yml
        inventory/sample/group_vars/all/coreos.yml
        inventory/sample/group_vars/all/docker.yml
        inventory/sample/group_vars/all/oci.yml
        inventory/sample/group_vars/all/openstack.yml

[wheel]
universal = 1

[pbr]
skip_authors = True
skip_changelog = True

[bdist_rpm]
group = "System Environment/Libraries"
requires =
    ansible
    python-jinja2
    python-netaddr
