---
- name: Check ansible version
  import_playbook: kubernetes_sigs.kubespray.ansible_version

- name: Install mitogen
  hosts: localhost
  strategy: linear
  vars:
    mitogen_version: 0.3.2
    mitogen_url: https://github.com/mitogen-hq/mitogen/archive/refs/tags/v{{ mitogen_version }}.tar.gz
    ansible_connection: local
  tasks:
    - name: Create mitogen plugin dir
      file:
        path: "{{ item }}"
        state: directory
        mode: 0755
      become: false
      loop:
        - "{{ playbook_dir }}/plugins/mitogen"
        - "{{ playbook_dir }}/dist"

    - name: Download mitogen release
      get_url:
        url: "{{ mitogen_url }}"
        dest: "{{ playbook_dir }}/dist/mitogen_{{ mitogen_version }}.tar.gz"
        validate_certs: true
        mode: 0644

    - name: Extract archive
      unarchive:
        src: "{{ playbook_dir }}/dist/mitogen_{{ mitogen_version }}.tar.gz"
        dest: "{{ playbook_dir }}/dist/"

    - name: Copy plugin
      ansible.posix.synchronize:
        src: "{{ playbook_dir }}/dist/mitogen-{{ mitogen_version }}/"
        dest: "{{ playbook_dir }}/plugins/mitogen"

    - name: Add strategy to ansible.cfg
      community.general.ini_file:
        path: ansible.cfg
        mode: 0644
        section: "{{ item.section | d('defaults') }}"
        option: "{{ item.option }}"
        value: "{{ item.value }}"
      with_items:
        - option: strategy
          value: mitogen_linear
        - option: strategy_plugins
          value: plugins/mitogen/ansible_mitogen/plugins/strategy
