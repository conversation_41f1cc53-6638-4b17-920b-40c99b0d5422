---
- name: Bootstrap hosts
  hosts: gfs-cluster
  gather_facts: false
  vars:
    ansible_ssh_pipelining: false
  roles:
    - { role: bootstrap-os, tags: bootstrap-os}

- name: Gather facts
  hosts: all
  gather_facts: true

- name: Install glusterfs server
  hosts: gfs-cluster
  vars:
    ansible_ssh_pipelining: true
  roles:
    - { role: glusterfs/server }

- name: Install glusterfs servers
  hosts: k8s_cluster
  roles:
    - { role: glusterfs/client }

- name: Configure Kubernetes to use glusterfs
  hosts: kube_control_plane[0]
  roles:
    - { role: kubernetes-pv }
