{
  "kind": "Endpoints",
  "apiVersion": "v1",
  "metadata": {
    "name": "glusterfs"
  },
  "subsets": [
    {% for host in groups['gfs-cluster'] %}
    {
      "addresses": [
        {
          "ip": "{{hostvars[host]['ip']|default(hostvars[host].ansible_default_ipv4['address'])}}"
        }
      ],
      "ports": [
        {
          "port": 1
        }
      ]
    }{%- if not loop.last %}, {% endif -%}
    {% endfor %}
  ]
}
