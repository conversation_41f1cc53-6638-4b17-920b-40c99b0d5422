---
- name: Kubernet<PERSON> Apps | Lay Down k8s GlusterFS Endpoint and PV
  template:
    src: "{{ item.file }}"
    dest: "{{ kube_config_dir }}/{{ item.dest }}"
    mode: 0644
  with_items:
    - { file: glusterfs-kubernetes-endpoint.json.j2, type: ep, dest: glusterfs-kubernetes-endpoint.json}
    - { file: glusterfs-kubernetes-pv.yml.j2, type: pv, dest: glusterfs-kubernetes-pv.yml}
    - { file: glusterfs-kubernetes-endpoint-svc.json.j2, type: svc, dest: glusterfs-kubernetes-endpoint-svc.json}
  register: gluster_pv
  when: inventory_hostname == groups['kube_control_plane'][0] and groups['gfs-cluster'] is defined and hostvars[groups['gfs-cluster'][0]].gluster_disk_size_gb is defined

- name: Kubernetes Apps | Set GlusterFS endpoint and PV
  kube:
    name: glusterfs
    namespace: default
    kubectl: "{{ bin_dir }}/kubectl"
    resource: "{{ item.item.type }}"
    filename: "{{ kube_config_dir }}/{{ item.item.dest }}"
    state: "{{ item.changed | ternary('latest', 'present') }}"
  with_items: "{{ gluster_pv.results }}"
  when: inventory_hostname == groups['kube_control_plane'][0] and groups['gfs-cluster'] is defined
