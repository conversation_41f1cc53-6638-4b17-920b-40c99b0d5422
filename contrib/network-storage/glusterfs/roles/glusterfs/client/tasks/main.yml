---
# This is meant for Ubuntu and RedHat installations, where apparently the glusterfs-client is not used from inside
# hyperkube and needs to be installed as part of the system.

# Setup/install tasks.
- name: Setup RedHat distros for glusterfs
  include_tasks: setup-RedHat.yml
  when: ansible_os_family == 'RedHat' and groups['gfs-cluster'] is defined

- name: Setup Debian distros for glusterfs
  include_tasks: setup-Debian.yml
  when: ansible_os_family == 'Debian' and groups['gfs-cluster'] is defined

- name: Ensure Gluster mount directories exist.
  file:
    path: "{{ item }}"
    state: directory
    mode: 0775
  with_items:
    - "{{ gluster_mount_dir }}"
  when: ansible_os_family in ["Debian","RedHat"] and groups['gfs-cluster'] is defined
