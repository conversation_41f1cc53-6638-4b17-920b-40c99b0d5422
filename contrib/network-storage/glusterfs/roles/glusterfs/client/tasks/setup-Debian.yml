---
- name: Add PPA for GlusterFS.
  apt_repository:
    repo: 'ppa:gluster/glusterfs-{{ glusterfs_ppa_version }}'
    state: present
    update_cache: yes
  register: glusterfs_ppa_added
  when: glusterfs_ppa_use

- name: Ensure GlusterFS client will reinstall if the PPA was just added.  # noqa no-handler
  apt:
    name: "{{ item }}"
    state: absent
  with_items:
    - glusterfs-client
  when: glusterfs_ppa_added.changed

- name: Ensure GlusterFS client is installed.
  apt:
    name: "{{ item }}"
    state: present
    default_release: "{{ glusterfs_default_release }}"
  with_items:
    - glusterfs-client
