# ## Configure 'ip' variable to bind kubernetes services on a
# ## different ip than the default iface
# node1 ansible_ssh_host=**********  # ip=********
# node2 ansible_ssh_host=**********  # ip=********
# node3 ansible_ssh_host=**********  # ip=********
# node4 ansible_ssh_host=**********  # ip=********
# node5 ansible_ssh_host=**********  # ip=********
# node6 ansible_ssh_host=**********  # ip=********
#
# ## GlusterFS nodes
# ## Set disk_volume_device_1 to desired device for gluster brick, if different to /dev/vdb (default).
# ## As in the previous case, you can set ip to give direct communication on internal IPs
# gfs_node1 ansible_ssh_host=********** # disk_volume_device_1=/dev/vdc  ip=********
# gfs_node2 ansible_ssh_host=********** # disk_volume_device_1=/dev/vdc  ip=********
# gfs_node3 ansible_ssh_host=********** # disk_volume_device_1=/dev/vdc  ip=********

# [kube_control_plane]
# node1
# node2

# [etcd]
# node1
# node2
# node3

# [kube_node]
# node2
# node3
# node4
# node5
# node6

# [k8s_cluster:children]
# kube_node
# kube_control_plane

# [gfs-cluster]
# gfs_node1
# gfs_node2
# gfs_node3

# [network-storage:children]
# gfs-cluster
