---
- name: "Load lvm kernel modules"
  become: true
  with_items:
    - "dm_snapshot"
    - "dm_mirror"
    - "dm_thin_pool"
  community.general.modprobe:
    name: "{{ item }}"
    state: "present"

- name: "Install glusterfs mount utils (RedHat)"
  become: true
  package:
    name: "glusterfs-fuse"
    state: "present"
  when: "ansible_os_family == 'RedHat'"

- name: "Install glusterfs mount utils (Debian)"
  become: true
  apt:
    name: "glusterfs-client"
    state: "present"
  when: "ansible_os_family == 'Debian'"
