---
- name: "Test heketi storage."
  command: "{{ bin_dir }}/kubectl get secrets,endpoints,services,jobs --output=json"
  changed_when: false
  register: "heketi_storage_state"
- name: "Create heketi storage."
  kube:
    name: "GlusterFS"
    kubectl: "{{ bin_dir }}/kubectl"
    filename: "{{ kube_config_dir }}/heketi-storage-bootstrap.json"
    state: "present"
  vars:
    secret_query: "items[?metadata.name=='heketi-storage-secret' && kind=='Secret']"
    endpoints_query: "items[?metadata.name=='heketi-storage-endpoints' && kind=='Endpoints']"
    service_query: "items[?metadata.name=='heketi-storage-endpoints' && kind=='Service']"
    job_query: "items[?metadata.name=='heketi-storage-copy-job' && kind=='Job']"
  when:
    - "heketi_storage_state.stdout | from_json | json_query(secret_query) | length == 0"
    - "heketi_storage_state.stdout | from_json | json_query(endpoints_query) | length == 0"
    - "heketi_storage_state.stdout | from_json | json_query(service_query) | length == 0"
    - "heketi_storage_state.stdout | from_json | json_query(job_query) | length == 0"
  register: "heketi_storage_result"
- name: "Get state of heketi database copy job."
  command: "{{ bin_dir }}/kubectl get jobs --output=json"
  changed_when: false
  register: "heketi_storage_state"
  vars:
    heketi_storage_state: { stdout: "{}" }
    job_query: "items[?metadata.name=='heketi-storage-copy-job' && kind=='Job' && status.succeeded==1]"
  until:
    - "heketi_storage_state.stdout | from_json | json_query(job_query) | length == 1"
  retries: 60
  delay: 5
