---
- name: "Kubernetes Apps | GlusterFS"
  include_tasks: "glusterfs.yml"

- name: "Kubernetes Apps | Heketi Secrets"
  include_tasks: "secret.yml"

- name: "Kubernetes Apps | Test Heketi"
  register: "heketi_service_state"
  command: "{{ bin_dir }}/kubectl get service heketi-storage-endpoints -o=name --ignore-not-found=true"
  changed_when: false

- name: "Kubernetes Apps | Bootstrap Heketi"
  when: "heketi_service_state.stdout == \"\""
  include_tasks: "bootstrap.yml"

- name: "Kubernetes Apps | Heketi"
  include_tasks: "heketi.yml"

- name: "Kubernetes Apps | Heketi Topology"
  include_tasks: "topology.yml"

- name: "Kubernetes Apps | Heketi Storage"
  include_tasks: "storage.yml"

- name: "Kubernetes Apps | Storage Class"
  include_tasks: "storageclass.yml"

- name: "Clean up"
  include_tasks: "cleanup.yml"
