---
- name: "Kubernetes Apps | Lay Down Heketi Storage"
  become: true
  vars: { nodes: "{{ groups['heketi-node'] }}" }
  template:
    src: "heketi-storage.json.j2"
    dest: "{{ kube_config_dir }}/heketi-storage.json"
    mode: 0644
  register: "rendering"
- name: "Kubernetes Apps | Install and configure Heketi Storage"
  kube:
    name: "GlusterFS"
    kubectl: "{{ bin_dir }}/kubectl"
    filename: "{{ kube_config_dir }}/heketi-storage.json"
    state: "{{ rendering.changed | ternary('latest', 'present') }}"
