---
- name: Get clusterrolebindings
  register: "clusterrolebinding_state"
  command: "{{ bin_dir }}/kubectl get clusterrolebinding heketi-gluster-admin -o=name --ignore-not-found=true"
  changed_when: false

- name: "Kubernetes Apps | Deploy cluster role binding."
  when: "clusterrolebinding_state.stdout | length == 0"
  command: "{{ bin_dir }}/kubectl create clusterrolebinding heketi-gluster-admin --clusterrole=edit --serviceaccount=default:heketi-service-account"

- name: Get clusterrolebindings again
  register: "clusterrolebinding_state"
  command: "{{ bin_dir }}/kubectl get clusterrolebinding heketi-gluster-admin -o=name --ignore-not-found=true"
  changed_when: false

- name: Make sure that clusterrolebindings are present now
  assert:
    that: "clusterrolebinding_state.stdout | length > 0"
    msg: "Cluster role binding is not present."

- name: Get the heketi-config-secret secret
  register: "secret_state"
  command: "{{ bin_dir }}/kubectl get secret heketi-config-secret -o=name --ignore-not-found=true"
  changed_when: false

- name: "Render Heketi secret configuration."
  become: true
  template:
    src: "heketi.json.j2"
    dest: "{{ kube_config_dir }}/heketi.json"
    mode: 0644

- name: "Deploy Heketi config secret"
  when: "secret_state.stdout | length == 0"
  command: "{{ bin_dir }}/kubectl create secret generic heketi-config-secret --from-file={{ kube_config_dir }}/heketi.json"

- name: Get the heketi-config-secret secret again
  register: "secret_state"
  command: "{{ bin_dir }}/kubectl get secret heketi-config-secret -o=name --ignore-not-found=true"
  changed_when: false

- name: Make sure the heketi-config-secret secret exists now
  assert:
    that: "secret_state.stdout | length > 0"
    msg: "Heketi config secret is not present."
