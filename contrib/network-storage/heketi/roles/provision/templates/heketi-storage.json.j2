{
  "apiVersion": "v1",
  "kind": "List",
  "items": [
    {
      "kind": "Endpoints",
      "apiVersion": "v1",
      "metadata": {
        "name": "heketi-storage-endpoints",
        "creationTimestamp": null
      },
      "subsets": [
{% set nodeblocks = [] %}
{% for node in nodes %}
{% set nodeblock %}
        {
          "addresses": [
            {
              "ip": "{{ hostvars[node].ip }}"
            }
          ],
          "ports": [
            {
              "port": 1
            }
          ]
        }
{% endset %}
{% if nodeblocks.append(nodeblock) %}{% endif %}
{% endfor %}
{{ nodeblocks|join(',') }}
      ]
    },
    {
      "kind": "Service",
      "apiVersion": "v1",
      "metadata": {
        "name": "heketi-storage-endpoints",
        "creationTimestamp": null
      },
      "spec": {
        "ports": [
          {
            "port": 1,
            "targetPort": 0
          }
        ]
      },
      "status": {
        "loadBalancer": {}
      }
    }
  ]
}
