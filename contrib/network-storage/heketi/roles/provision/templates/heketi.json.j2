{"_port_comment": "Heketi Server Port Number", "port": "8080", "_use_auth": "Enable JWT authorization. Please enable for deployment", "use_auth": true, "_jwt": "Private keys for access", "jwt": {"_admin": "Admin has access to all APIs", "admin": {"key": "{{ heketi_admin_key }}"}, "_user": "User only has access to /volumes endpoint", "user": {"key": "{{ heketi_user_key }}"}}, "_glusterfs_comment": "GlusterFS Configuration", "glusterfs": {"_executor_comment": "Execute plugin. Possible choices: mock, kubernetes, ssh", "executor": "kubernetes", "_db_comment": "Database file name", "db": "/var/lib/heketi/heketi.db", "kubeexec": {"rebalance_on_expansion": true}, "sshexec": {"rebalance_on_expansion": true, "keyfile": "/etc/heketi/private_key", "fstab": "/etc/fstab", "port": "22", "user": "root", "sudo": false}}, "_backup_db_to_kube_secret": "Backup the heketi database to a Kubernetes secret when running in Kubernetes. <PERSON><PERSON><PERSON> is off.", "backup_db_to_kube_secret": false}