{"kind": "List", "apiVersion": "v1", "items": [{"kind": "Service", "apiVersion": "v1", "metadata": {"name": "deploy-<PERSON><PERSON><PERSON>", "labels": {"glusterfs": "heketi-service", "deploy-heketi": "support"}, "annotations": {"description": "Exposes Heketi Service"}}, "spec": {"selector": {"name": "deploy-<PERSON><PERSON><PERSON>"}, "ports": [{"name": "deploy-<PERSON><PERSON><PERSON>", "port": 8080, "targetPort": 8080}]}}, {"kind": "Deployment", "apiVersion": "apps/v1", "metadata": {"name": "deploy-<PERSON><PERSON><PERSON>", "labels": {"glusterfs": "heketi-deployment", "deploy-heketi": "deployment"}, "annotations": {"description": "Defines how to deploy Heketi"}}, "spec": {"selector": {"matchLabels": {"name": "deploy-<PERSON><PERSON><PERSON>"}}, "replicas": 1, "template": {"metadata": {"name": "deploy-<PERSON><PERSON><PERSON>", "labels": {"name": "deploy-<PERSON><PERSON><PERSON>", "glusterfs": "heketi-pod", "deploy-heketi": "pod"}}, "spec": {"serviceAccountName": "heketi-service-account", "containers": [{"image": "heketi/heketi:9", "imagePullPolicy": "Always", "name": "deploy-<PERSON><PERSON><PERSON>", "env": [{"name": "HEKETI_EXECUTOR", "value": "kubernetes"}, {"name": "HEKETI_DB_PATH", "value": "/var/lib/heketi/heketi.db"}, {"name": "HEKETI_FSTAB", "value": "/var/lib/heketi/fstab"}, {"name": "HEKETI_SNAPSHOT_LIMIT", "value": "14"}, {"name": "HEKETI_KUBE_GLUSTER_DAEMONSET", "value": "y"}], "ports": [{"containerPort": 8080}], "volumeMounts": [{"name": "db", "mountPath": "/var/lib/heketi"}, {"name": "config", "mountPath": "/etc/heketi"}], "readinessProbe": {"timeoutSeconds": 3, "initialDelaySeconds": 3, "httpGet": {"path": "/hello", "port": 8080}}, "livenessProbe": {"timeoutSeconds": 3, "initialDelaySeconds": 10, "httpGet": {"path": "/hello", "port": 8080}}}], "volumes": [{"name": "db"}, {"name": "config", "secret": {"secretName": "heketi-config-secret"}}]}}}}]}