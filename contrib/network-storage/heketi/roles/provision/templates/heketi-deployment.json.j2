{"kind": "List", "apiVersion": "v1", "items": [{"kind": "Secret", "apiVersion": "v1", "metadata": {"name": "heketi-db-backup", "labels": {"glusterfs": "heketi-db", "heketi": "db"}}, "data": {}, "type": "Opaque"}, {"kind": "Service", "apiVersion": "v1", "metadata": {"name": "<PERSON><PERSON>i", "labels": {"glusterfs": "heketi-service", "deploy-heketi": "support"}, "annotations": {"description": "Exposes Heketi Service"}}, "spec": {"selector": {"name": "<PERSON><PERSON>i"}, "ports": [{"name": "<PERSON><PERSON>i", "port": 8080, "targetPort": 8080}]}}, {"kind": "Deployment", "apiVersion": "apps/v1", "metadata": {"name": "<PERSON><PERSON>i", "labels": {"glusterfs": "heketi-deployment"}, "annotations": {"description": "Defines how to deploy Heketi"}}, "spec": {"selector": {"matchLabels": {"name": "<PERSON><PERSON>i"}}, "replicas": 1, "template": {"metadata": {"name": "<PERSON><PERSON>i", "labels": {"name": "<PERSON><PERSON>i", "glusterfs": "heketi-pod"}}, "spec": {"serviceAccountName": "heketi-service-account", "containers": [{"image": "heketi/heketi:9", "imagePullPolicy": "Always", "name": "<PERSON><PERSON>i", "env": [{"name": "HEKETI_EXECUTOR", "value": "kubernetes"}, {"name": "HEKETI_DB_PATH", "value": "/var/lib/heketi/heketi.db"}, {"name": "HEKETI_FSTAB", "value": "/var/lib/heketi/fstab"}, {"name": "HEKETI_SNAPSHOT_LIMIT", "value": "14"}, {"name": "HEKETI_KUBE_GLUSTER_DAEMONSET", "value": "y"}], "ports": [{"containerPort": 8080}], "volumeMounts": [{"mountPath": "/backupdb", "name": "heketi-db-secret"}, {"name": "db", "mountPath": "/var/lib/heketi"}, {"name": "config", "mountPath": "/etc/heketi"}], "readinessProbe": {"timeoutSeconds": 3, "initialDelaySeconds": 3, "httpGet": {"path": "/hello", "port": 8080}}, "livenessProbe": {"timeoutSeconds": 3, "initialDelaySeconds": 10, "httpGet": {"path": "/hello", "port": 8080}}}], "volumes": [{"name": "db", "glusterfs": {"endpoints": "heketi-storage-endpoints", "path": "heketidbstorage"}}, {"name": "heketi-db-secret", "secret": {"secretName": "heketi-db-backup"}}, {"name": "config", "secret": {"secretName": "heketi-config-secret"}}]}}}}]}