{
  "clusters": [
    {
      "nodes": [
{% set nodeblocks = [] %}
{% for node in nodes %}
{% set nodeblock %}
        {
          "node": {
            "hostnames": {
              "manage": [
                "{{ node }}"
              ],
              "storage": [
                "{{ hostvars[node].ip }}"
              ]
            },
            "zone": 1
          },
          "devices": [
            {
              "name": "{{ hostvars[node]['disk_volume_device_1'] }}",
              "destroydata": false
            }
          ]
        }
{% endset %}
{% if nodeblocks.append(nodeblock) %}{% endif %}
{% endfor %}
{{ nodeblocks|join(',') }}
      ]
    }
  ]
}
