---
- name: Disable firewalld and ufw
  when:
  - disable_service_firewall is defined and disable_service_firewall
  block:
  - name: List services
    service_facts:

  - name: Disable service firewalld
    systemd:
      name: firewalld
      state: stopped
      enabled: no
    when:
      "'firewalld.service' in services and services['firewalld.service'].status != 'not-found'"

  - name: Disable service ufw
    systemd:
      name: ufw
      state: stopped
      enabled: no
    when:
      "'ufw.service' in services and services['ufw.service'].status != 'not-found'"
