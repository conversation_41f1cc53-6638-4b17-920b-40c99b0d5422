---
distro_settings:
  debian: &DEBIAN
    image: "debian:9.5"
    user: "debian"
    pid1_exe: /lib/systemd/systemd
    init: |
      sh -c "apt-get -qy update && apt-get -qy install systemd-sysv dbus && exec /sbin/init"
    raw_setup: apt-get -qy update && apt-get -qy install dbus python sudo iproute2
    raw_setup_done: test -x /usr/bin/sudo
    agetty_svc: getty@*
    ssh_service: ssh
    extra_packages: []
  ubuntu:
    <<: *DEBIAN
    image: "ubuntu:16.04"
    user: "ubuntu"
    init: |
      /sbin/init
  centos: &CENTOS
    image: "centos:7"
    user: "centos"
    pid1_exe: /usr/lib/systemd/systemd
    init: |
      /sbin/init
    raw_setup: yum -qy install policycoreutils dbus python sudo iproute iptables
    raw_setup_done: test -x /usr/bin/sudo
    agetty_svc: getty@* serial-getty@*
    ssh_service: sshd
    extra_packages: []
  fedora:
    <<: *CENTOS
    image: "fedora:latest"
    user: "fedora"
    raw_setup: yum -qy install policycoreutils dbus python sudo iproute iptables; mkdir -p /etc/modules-load.d
    extra_packages:
      - hostname
      - procps
      - findutils
      - kmod
      - iputils
