---
apiVersion: "2015-06-15"

virtualNetworkName: "{{ azure_virtual_network_name | default('KubeVNET') }}"

subnetAdminName: "{{ azure_subnet_admin_name | default('ad-subnet') }}"
subnetMastersName: "{{ azure_subnet_masters_name | default('master-subnet') }}"
subnetMinionsName: "{{ azure_subnet_minions_name | default('minion-subnet') }}"

routeTableName: "{{ azure_route_table_name | default('routetable') }}"
securityGroupName: "{{ azure_security_group_name | default('secgroup') }}"

nameSuffix: "{{ cluster_name }}"

availabilitySetMasters: "master-avs"
availabilitySetMinions: "minion-avs"

faultDomainCount: 3
updateDomainCount: 10

bastionVmSize: Standard_A0
bastionVMName: bastion
bastionIPAddressName: bastion-pubip

disablePasswordAuthentication: true

sshKeyPath: "/home/<USER>/.ssh/authorized_keys"

imageReference:
  publisher: "OpenLogic"
  offer: "CentOS"
  sku: "7.5"
  version: "latest"
imageReferenceJson: "{{ imageReference | to_json }}"

storageAccountName: "sa{{ nameSuffix | replace('-', '') }}"
storageAccountType: "{{ azure_storage_account_type | default('Standard_LRS') }}"
