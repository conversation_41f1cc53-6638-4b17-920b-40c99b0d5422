{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "1.0.0.0", "parameters": {}, "variables": {}, "resources": [{"type": "Microsoft.Compute/availabilitySets", "name": "{{availabilitySetMasters}}", "apiVersion": "{{api<PERSON><PERSON><PERSON>}}", "location": "[resourceGroup().location]", "properties": {"PlatformFaultDomainCount": "{{faultDomainCount}}", "PlatformUpdateDomainCount": "{{updateDomainCount}}"}}, {"type": "Microsoft.Compute/availabilitySets", "name": "{{availabilitySetMinions}}", "apiVersion": "{{api<PERSON><PERSON><PERSON>}}", "location": "[resourceGroup().location]", "properties": {"PlatformFaultDomainCount": "{{faultDomainCount}}", "PlatformUpdateDomainCount": "{{updateDomainCount}}"}}]}