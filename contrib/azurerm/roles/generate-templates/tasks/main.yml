---
- name: Set base_dir
  set_fact:
    base_dir: "{{ playbook_dir }}/.generated/"

- name: Create base_dir
  file:
    path: "{{ base_dir }}"
    state: directory
    recurse: true
    mode: 0755

- name: Store json files in base_dir
  template:
    src: "{{ item }}"
    dest: "{{ base_dir }}/{{ item }}"
    mode: 0644
  with_items:
    - network.json
    - storage.json
    - availability-sets.json
    - bastion.json
    - masters.json
    - minions.json
    - clear-rg.json
