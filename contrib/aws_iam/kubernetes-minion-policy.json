{"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": "s3:*", "Resource": ["arn:aws:s3:::kubernetes-*"]}, {"Effect": "Allow", "Action": "ec2:Describe*", "Resource": "*"}, {"Effect": "Allow", "Action": "ec2:AttachVolume", "Resource": "*"}, {"Effect": "Allow", "Action": "ec2:DetachVolume", "Resource": "*"}, {"Effect": "Allow", "Action": ["route53:*"], "Resource": ["*"]}, {"Effect": "Allow", "Action": ["ecr:GetAuthorizationToken", "ecr:BatchCheckLayerAvailability", "ecr:GetDownloadUrlForLayer", "ecr:GetRepositoryPolicy", "ecr:DescribeRepositories", "ecr:ListImages", "ecr:BatchGetImage"], "Resource": "*"}]}