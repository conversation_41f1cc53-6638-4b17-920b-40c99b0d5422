[tox]
minversion = 1.6
skipsdist = True
envlist = pep8

[testenv]
allowlist_externals = py.test
usedevelop = True
deps =
    -r{toxinidir}/requirements.txt
    -r{toxinidir}/test-requirements.txt
setenv = VIRTUAL_ENV={envdir}
passenv =
    http_proxy
    HTTP_PROXY
    https_proxy
    HTTPS_PROXY
    no_proxy
    NO_PROXY
commands = pytest -vv #{posargs:./tests}

[testenv:pep8]
usedevelop = False
allowlist_externals = bash
commands =
    bash -c "find {toxinidir}/* -type f -name '*.py' -print0 | xargs -0 flake8"

[testenv:venv]
commands = {posargs}

[flake8]
show-source = true
builtins = _
exclude=.venv,.git,.tox,dist,doc,*lib/python*,*egg
