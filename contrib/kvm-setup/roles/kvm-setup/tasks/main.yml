---

- name: Install required packages
  package:
    name: "{{ item }}"
    state: present
  with_items:
    - bind-utils
    - ntp
  when: ansible_os_family == "RedHat"

- name: Install required packages
  apt:
    upgrade: yes
    update_cache: yes
    cache_valid_time: 3600
    name: "{{ item }}"
    state: present
    install_recommends: no
  with_items:
    - dnsutils
    - ntp
  when: ansible_os_family == "Debian"

- name: Create deployment user if required
  include_tasks: user.yml
  when: k8s_deployment_user is defined

- name: Set proper sysctl values
  import_tasks: sysctl.yml
