---
- name: Create user {{ k8s_deployment_user }}
  user:
    name: "{{ k8s_deployment_user }}"
    groups: adm
    shell: /bin/bash

- name: Ensure that .ssh exists
  file:
    path: "/home/<USER>/.ssh"
    state: directory
    owner: "{{ k8s_deployment_user }}"
    group: "{{ k8s_deployment_user }}"
    mode: 0700

- name: Configure sudo for deployment user
  copy:
    content: |
      %{{ k8s_deployment_user }} ALL=(ALL) NOPASSWD: ALL
    dest: "/etc/sudoers.d/55-k8s-deployment"
    owner: root
    group: root
    mode: 0644

- name: Write private SSH key
  copy:
    src: "{{ k8s_deployment_user_pkey_path }}"
    dest: "/home/<USER>/.ssh/id_rsa"
    mode: 0400
    owner: "{{ k8s_deployment_user }}"
    group: "{{ k8s_deployment_user }}"
  when: k8s_deployment_user_pkey_path is defined

- name: Write public SSH key
  shell: "ssh-keygen -y -f /home/<USER>/.ssh/id_rsa \
            > /home/<USER>/.ssh/authorized_keys"
  args:
    creates: "/home/<USER>/.ssh/authorized_keys"
  when: k8s_deployment_user_pkey_path is defined

- name: Fix ssh-pub-key permissions
  file:
    path: "/home/<USER>/.ssh/authorized_keys"
    mode: 0600
    owner: "{{ k8s_deployment_user }}"
    group: "{{ k8s_deployment_user }}"
  when: k8s_deployment_user_pkey_path is defined
