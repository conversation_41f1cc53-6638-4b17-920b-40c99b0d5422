{"gcp_project_id": "GCP_PROJECT_ID", "region": "us-central1", "ssh_pub_key": "~/.ssh/id_rsa.pub", "keyfile_location": "service-account.json", "prefix": "development", "ssh_whitelist": ["1.2.3.4/32"], "api_server_whitelist": ["1.2.3.4/32"], "nodeport_whitelist": ["1.2.3.4/32"], "ingress_whitelist": ["0.0.0.0/0"], "machines": {"master-0": {"node_type": "master", "size": "n1-standard-2", "zone": "us-central1-a", "additional_disks": {}, "boot_disk": {"image_name": "ubuntu-os-cloud/ubuntu-2004-focal-v20220118", "size": 50}}, "worker-0": {"node_type": "worker", "size": "n1-standard-8", "zone": "us-central1-a", "additional_disks": {"extra-disk-1": {"size": 100}}, "boot_disk": {"image_name": "ubuntu-os-cloud/ubuntu-2004-focal-v20220118", "size": 50}}, "worker-1": {"node_type": "worker", "size": "n1-standard-8", "zone": "us-central1-a", "additional_disks": {"extra-disk-1": {"size": 100}}, "boot_disk": {"image_name": "ubuntu-os-cloud/ubuntu-2004-focal-v20220118", "size": 50}}}}