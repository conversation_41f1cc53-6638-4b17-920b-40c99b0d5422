# Copyright 2013 Hewlett-Packard Development Company, L.P.
# All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License"); you may
# not use this file except in compliance with the License. You may obtain
# a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
# License for the specific language governing permissions and limitations
# under the License.

from pbr.hooks import backwards
from pbr.hooks import commands
from pbr.hooks import files
from pbr.hooks import metadata


def setup_hook(config):
    """Filter config parsed from a setup.cfg to inject our defaults."""
    metadata_config = metadata.MetadataConfig(config)
    metadata_config.run()
    backwards.BackwardsCompatConfig(config).run()
    commands.CommandsConfig(config).run()
    files.FilesConfig(config, metadata_config.get_name()).run()
