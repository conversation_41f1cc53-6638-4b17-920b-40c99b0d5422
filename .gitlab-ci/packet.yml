---
.packet:
  extends: .testcases
  variables:
    ANSIBLE_TIMEOUT: "120"
    CI_PLATFORM: packet
    SSH_USER: kubespray
  tags:
    - packet
  except: [triggers]

# CI template for PRs
.packet_pr:
  only: [/^pr-.*$/]
  extends: .packet

# CI template for periodic CI jobs
# Enabled when PERIODIC_CI_ENABLED var is set
.packet_periodic:
  only:
    variables:
      - $PERIODIC_CI_ENABLED
  allow_failure: true
  extends: .packet

packet_cleanup_old:
  stage: deploy-part1
  extends: .packet_periodic
  script:
    - cd tests
    - make cleanup-packet
  after_script: []

# The ubuntu20-calico-all-in-one jobs are meant as early stages to prevent running the full CI if something is horribly broken
packet_ubuntu20-calico-all-in-one:
  stage: deploy-part1
  extends: .packet_pr
  when: on_success
  variables:
    RESET_CHECK: "true"

# ### PR JOBS PART2

packet_ubuntu20-all-in-one-docker:
  stage: deploy-part2
  extends: .packet_pr
  when: on_success

packet_ubuntu20-calico-all-in-one-hardening:
  stage: deploy-part2
  extends: .packet_pr
  when: on_success

packet_ubuntu22-all-in-one-docker:
  stage: deploy-part2
  extends: .packet_pr
  when: on_success

packet_ubuntu22-calico-all-in-one:
  stage: deploy-part2
  extends: .packet_pr
  when: on_success

packet_ubuntu24-all-in-one-docker:
  stage: deploy-part2
  extends: .packet_pr
  when: on_success

packet_ubuntu24-calico-all-in-one:
  stage: deploy-part2
  extends: .packet_pr
  when: on_success

packet_ubuntu24-calico-etcd-datastore:
  stage: deploy-part2
  extends: .packet_pr
  when: on_success

packet_centos7-flannel-addons-ha:
  extends: .packet_pr
  stage: deploy-part2
  when: on_success

packet_almalinux8-crio:
  extends: .packet_pr
  stage: deploy-part2
  when: on_success
  allow_failure: true

packet_ubuntu20-crio:
  extends: .packet_pr
  stage: deploy-part2
  when: manual

packet_fedora37-crio:
  extends: .packet_pr
  stage: deploy-part2
  when: manual

packet_ubuntu20-flannel-ha:
  stage: deploy-part2
  extends: .packet_pr
  when: manual

packet_debian10-cilium-svc-proxy:
  stage: deploy-part2
  extends: .packet_periodic
  when: on_success

packet_debian10-calico:
  stage: deploy-part2
  extends: .packet_pr
  when: on_success

packet_debian10-docker:
  stage: deploy-part2
  extends: .packet_pr
  when: on_success

packet_debian11-calico:
  stage: deploy-part2
  extends: .packet_pr
  when: on_success

packet_debian11-docker:
  stage: deploy-part2
  extends: .packet_pr
  when: on_success

packet_debian12-calico:
  stage: deploy-part2
  extends: .packet_pr
  when: on_success

packet_debian12-docker:
  stage: deploy-part2
  extends: .packet_pr
  when: on_success

packet_debian12-cilium:
  stage: deploy-part2
  extends: .packet_periodic
  when: on_success

packet_centos7-calico-ha-once-localhost:
  stage: deploy-part2
  extends: .packet_pr
  when: on_success
  variables:
    # This will instruct Docker not to start over TLS.
    DOCKER_TLS_CERTDIR: ""
  services:
    - docker:19.03.9-dind

packet_almalinux8-kube-ovn:
  stage: deploy-part2
  extends: .packet_pr
  when: on_success

packet_almalinux8-calico:
  stage: deploy-part2
  extends: .packet_pr
  when: on_success

packet_rockylinux8-calico:
  stage: deploy-part2
  extends: .packet_pr
  when: on_success

packet_rockylinux9-calico:
  stage: deploy-part2
  extends: .packet_pr
  when: on_success

packet_rockylinux9-cilium:
  stage: deploy-part2
  extends: .packet_pr
  when: on_success
  variables:
    RESET_CHECK: "true"

packet_almalinux8-docker:
  stage: deploy-part2
  extends: .packet_pr
  when: on_success

packet_amazon-linux-2-all-in-one:
  stage: deploy-part2
  extends: .packet_pr
  when: on_success

packet_fedora38-docker-weave:
  stage: deploy-part2
  extends: .packet_pr
  when: on_success
  allow_failure: true

packet_opensuse-docker-cilium:
  stage: deploy-part2
  extends: .packet_pr
  when: on_success

# ### MANUAL JOBS

packet_ubuntu20-docker-weave-sep:
  stage: deploy-part2
  extends: .packet_pr
  when: manual

packet_ubuntu20-cilium-sep:
  stage: deploy-special
  extends: .packet_pr
  when: manual

packet_ubuntu20-flannel-ha-once:
  stage: deploy-part2
  extends: .packet_pr
  when: manual

# Calico HA eBPF
packet_almalinux8-calico-ha-ebpf:
  stage: deploy-part2
  extends: .packet_pr
  when: manual

packet_debian10-macvlan:
  stage: deploy-part2
  extends: .packet_pr
  when: manual

packet_centos7-calico-ha:
  stage: deploy-part2
  extends: .packet_pr
  when: manual

packet_centos7-multus-calico:
  stage: deploy-part2
  extends: .packet_pr
  when: manual

packet_fedora38-docker-calico:
  stage: deploy-part2
  extends: .packet_periodic
  when: on_success
  variables:
    RESET_CHECK: "true"

packet_fedora37-calico-selinux:
  stage: deploy-part2
  extends: .packet_periodic
  when: on_success

packet_fedora37-calico-swap-selinux:
  stage: deploy-part2
  extends: .packet_pr
  when: manual

packet_almalinux8-calico-nodelocaldns-secondary:
  stage: deploy-part2
  extends: .packet_pr
  when: manual

packet_fedora38-kube-ovn:
  stage: deploy-part2
  extends: .packet_periodic
  when: on_success

packet_debian11-custom-cni:
  stage: deploy-part2
  extends: .packet_pr
  when: manual

packet_debian11-kubelet-csr-approver:
  stage: deploy-part2
  extends: .packet_pr
  when: manual

packet_debian12-custom-cni-helm:
  stage: deploy-part2
  extends: .packet_pr
  when: manual

# ### PR JOBS PART3
# Long jobs (45min+)

packet_centos7-weave-upgrade-ha:
  stage: deploy-part3
  extends: .packet_periodic
  when: on_success
  variables:
    UPGRADE_TEST: basic

packet_ubuntu20-calico-etcd-kubeadm-upgrade-ha:
  stage: deploy-part3
  extends: .packet_periodic
  when: on_success
  variables:
    UPGRADE_TEST: basic

# Calico HA Wireguard
packet_ubuntu20-calico-ha-wireguard:
  stage: deploy-part2
  extends: .packet_pr
  when: manual

packet_debian11-calico-upgrade:
  stage: deploy-part3
  extends: .packet_pr
  when: on_success
  variables:
    UPGRADE_TEST: graceful

packet_almalinux8-calico-remove-node:
  stage: deploy-part3
  extends: .packet_pr
  when: on_success
  variables:
    REMOVE_NODE_CHECK: "true"
    REMOVE_NODE_NAME: "instance-3"

packet_ubuntu20-calico-etcd-kubeadm:
  stage: deploy-part3
  extends: .packet_pr
  when: on_success

packet_debian11-calico-upgrade-once:
  stage: deploy-part3
  extends: .packet_periodic
  when: on_success
  variables:
    UPGRADE_TEST: graceful

packet_ubuntu20-calico-ha-recover:
  stage: deploy-part3
  extends: .packet_periodic
  when: on_success
  variables:
    RECOVER_CONTROL_PLANE_TEST: "true"
    RECOVER_CONTROL_PLANE_TEST_GROUPS: "etcd[2:]:kube_control_plane[1:]"

packet_ubuntu20-calico-ha-recover-noquorum:
  stage: deploy-part3
  extends: .packet_periodic
  when: on_success
  variables:
    RECOVER_CONTROL_PLANE_TEST: "true"
    RECOVER_CONTROL_PLANE_TEST_GROUPS: "etcd[1:]:kube_control_plane[1:]"
