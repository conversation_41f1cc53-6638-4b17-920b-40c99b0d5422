---
shellcheck:
  extends: .job
  stage: unit-tests
  tags: [light]
  variables:
    SHELLCHECK_VERSION: v0.7.1
  before_script:
    - ./tests/scripts/rebase.sh
    - curl --silent --location "https://github.com/koalaman/shellcheck/releases/download/"${SH<PERSON>LCHECK_VERSION}"/shellcheck-"${SHELLCHECK_VERSION}".linux.x86_64.tar.xz" | tar -xJv
    - cp shellcheck-"${SHELLCHECK_VERSION}"/shellcheck /usr/bin/
    - shellcheck --version
  script:
    # Run shellcheck for all *.sh
    - find . -name '*.sh' -not -path './.git/*' | xargs shellcheck --severity error
  except: ['triggers', 'master']
