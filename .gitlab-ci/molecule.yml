---

.molecule:
  tags: [c3.small.x86]
  only: [/^pr-.*$/]
  except: ['triggers']
  image: $PIPELINE_IMAGE
  services: []
  stage: deploy-part1
  before_script:
    - tests/scripts/rebase.sh
    - ./tests/scripts/vagrant_clean.sh
  script:
    - ./tests/scripts/molecule_run.sh
  after_script:
    - chronic ./tests/scripts/molecule_logs.sh
  artifacts:
    when: always
    paths:
      - molecule_logs/

# CI template for periodic CI jobs
# Enabled when PERIODIC_CI_ENABLED var is set
.molecule_periodic:
  only:
    variables:
      - $PERIODIC_CI_ENABLED
  allow_failure: true
  extends: .molecule

molecule_full:
  extends: .molecule_periodic

molecule_no_container_engines:
  extends: .molecule
  script:
    - ./tests/scripts/molecule_run.sh -e container-engine
  when: on_success

molecule_docker:
  extends: .molecule
  script:
    - ./tests/scripts/molecule_run.sh -i container-engine/cri-dockerd
  when: on_success

molecule_containerd:
  extends: .molecule
  script:
    - ./tests/scripts/molecule_run.sh -i container-engine/containerd
  when: on_success

molecule_cri-o:
  extends: .molecule
  stage: deploy-part2
  script:
    - ./tests/scripts/molecule_run.sh -i container-engine/cri-o
  allow_failure: true
  when: on_success

# Stage 3 container engines don't get as much attention so allow them to fail
molecule_kata:
  extends: .molecule
  stage: deploy-part3
  script:
    - ./tests/scripts/molecule_run.sh -i container-engine/kata-containers
  when: manual
# FIXME: this test is broken (perma-failing)

molecule_gvisor:
  extends: .molecule
  stage: deploy-part3
  script:
    - ./tests/scripts/molecule_run.sh -i container-engine/gvisor
  when: manual
# FIXME: this test is broken (perma-failing)

molecule_youki:
  extends: .molecule
  stage: deploy-part3
  script:
    - ./tests/scripts/molecule_run.sh -i container-engine/youki
  when: manual
# FIXME: this test is broken (perma-failing)
