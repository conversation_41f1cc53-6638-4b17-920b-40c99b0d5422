---
namespace: kubernetes_sigs
description: Deploy a production ready Kubernetes cluster
name: kubespray
version: 2.25.0
readme: README.md
authors:
  - The Kubespray maintainers (https://kubernetes.slack.com/channels/kubespray)
tags:
  - infrastructure
repository: https://github.com/kubernetes-sigs/kubespray
license_file: LICENSE
dependencies:
  ansible.utils: '>=2.5.0'
  community.general: '>=3.0.0'
build_ignore:
  - .github
  - '*.tar.gz'
  - extra_playbooks
  - inventory
  - scripts
  - test-infra
  - .ansible-lint
  - .editorconfig
  - .gitignore
  - .gitlab-ci
  - .gitlab-ci.yml
  - .gitmodules
  - .markdownlint.yaml
  - .nojekyll
  - .pre-commit-config.yaml
  - .yamllint
  - Dockerfile
  - FILES.json
  - MANIFEST.json
  - Makefile
  - Vagrantfile
  - _config.yml
  - ansible.cfg
  - requirements*txt
  - setup.cfg
  - setup.py
  - index.html
  - reset.yml
  - cluster.yml
  - scale.yml
  - recover-control-plane.yml
  - remove-node.yml
  - upgrade-cluster.yml
  - library
