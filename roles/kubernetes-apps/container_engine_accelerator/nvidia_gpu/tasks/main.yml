---

- name: Container Engine Acceleration Nvidia GPU | gather os specific variables
  include_vars: "{{ item }}"
  with_first_found:
    - files:
        - "{{ ansible_distribution | lower }}-{{ ansible_distribution_version | lower | replace('/', '_') }}.yml"
        - "{{ ansible_distribution | lower }}-{{ ansible_distribution_release }}.yml"
        - "{{ ansible_distribution | lower }}-{{ ansible_distribution_major_version | lower | replace('/', '_') }}.yml"
        - "{{ ansible_distribution | lower }}.yml"
        - "{{ ansible_os_family | lower }}.yml"
      skip: true

- name: Container Engine Acceleration Nvidia GPU | Set fact of download url Tesla
  set_fact:
    nvidia_driver_download_url_default: "{{ nvidia_gpu_tesla_base_url }}{{ nvidia_url_end }}"
  when: nvidia_gpu_flavor | lower == "tesla"

- name: Container Engine Acceleration Nvidia GPU | Set fact of download url GTX
  set_fact:
    nvidia_driver_download_url_default: "{{ nvidia_gpu_gtx_base_url }}{{ nvidia_url_end }}"
  when: nvidia_gpu_flavor | lower == "gtx"

- name: Container Engine Acceleration Nvidia GPU | Create addon dir
  file:
    path: "{{ kube_config_dir }}/addons/container_engine_accelerator"
    owner: root
    group: root
    mode: 0755
    recurse: true

- name: Container Engine Acceleration Nvidia GPU | Create manifests for nvidia accelerators
  template:
    src: "{{ item.file }}.j2"
    dest: "{{ kube_config_dir }}/addons/container_engine_accelerator/{{ item.file }}"
    mode: 0644
  with_items:
    - { name: nvidia-driver-install-daemonset, file: nvidia-driver-install-daemonset.yml, type: daemonset }
    - { name: k8s-device-plugin-nvidia-daemonset, file: k8s-device-plugin-nvidia-daemonset.yml, type: daemonset }
  register: container_engine_accelerator_manifests
  when:
    - inventory_hostname == groups['kube_control_plane'][0] and nvidia_driver_install_container

- name: Container Engine Acceleration Nvidia GPU | Apply manifests for nvidia accelerators
  kube:
    name: "{{ item.item.name }}"
    namespace: "kube-system"
    kubectl: "{{ bin_dir }}/kubectl"
    resource: "{{ item.item.type }}"
    filename: "{{ kube_config_dir }}/addons/container_engine_accelerator/{{ item.item.file }}"
    state: "latest"
  with_items:
    - "{{ container_engine_accelerator_manifests.results }}"
  when:
    - inventory_hostname == groups['kube_control_plane'][0] and nvidia_driver_install_container and nvidia_driver_install_supported
