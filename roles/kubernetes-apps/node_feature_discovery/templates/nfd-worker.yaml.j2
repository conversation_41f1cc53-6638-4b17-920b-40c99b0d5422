apiVersion: apps/v1
kind: DaemonSet
metadata:
  name:  node-feature-discovery-worker
  namespace: {{ node_feature_discovery_namespace }}
  labels:
    app.kubernetes.io/name: node-feature-discovery
    role: worker
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: node-feature-discovery
      role: worker
  template:
    metadata:
      labels:
        app.kubernetes.io/name: node-feature-discovery
        role: worker
    spec:
      dnsPolicy: ClusterFirstWithHostNet
      serviceAccountName: {{ node_feature_discovery_worker_sa_name }}
      containers:
      - name: worker
        securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: true
            runAsNonRoot: true
        image: {{ node_feature_discovery_image_repo }}:{{ node_feature_discovery_image_tag }}
        imagePullPolicy: IfNotPresent
        env:
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        command:
        - "nfd-worker"
        args:
        - "-server=node-feature-discovery-master:8080"
{% if not node_feature_discovery_enable_nodefeature_api %}
        - "-enable-nodefeature-api=false"
{% endif %}
        - "-metrics=8081"
        ports:
          - name: metrics
            containerPort: 8081
        volumeMounts:
        - name: host-boot
          mountPath: "/host-boot"
          readOnly: true
        - name: host-os-release
          mountPath: "/host-etc/os-release"
          readOnly: true
        - name: host-sys
          mountPath: "/host-sys"
          readOnly: true
        - name: host-usr-lib
          mountPath: "/host-usr/lib"
          readOnly: true
        - name: host-lib
          mountPath: "/host-lib"
          readOnly: true
        - name: source-d
          mountPath: "/etc/kubernetes/node-feature-discovery/source.d/"
          readOnly: true
        - name: features-d
          mountPath: "/etc/kubernetes/node-feature-discovery/features.d/"
          readOnly: true
        - name: nfd-worker-conf
          mountPath: "/etc/kubernetes/node-feature-discovery"
          readOnly: true
      volumes:
        - name: host-boot
          hostPath:
            path: "/boot"
        - name: host-os-release
          hostPath:
            path: "/etc/os-release"
        - name: host-sys
          hostPath:
            path: "/sys"
        - name: host-usr-lib
          hostPath:
            path: "/usr/lib"
        - name: host-lib
          hostPath:
            path: "/lib"
        - name: source-d
          hostPath:
            path: "/etc/kubernetes/node-feature-discovery/source.d/"
        - name: features-d
          hostPath:
            path: "/etc/kubernetes/node-feature-discovery/features.d/"
        - name: nfd-worker-conf
          configMap:
            name: node-feature-discovery-worker-conf
            items:
              - key: nfd-worker.conf
                path: nfd-worker.conf
{% if node_feature_discovery_worker_tolerations %}
      tolerations:
        {{ node_feature_discovery_worker_tolerations | to_yaml(indent=2, width=1337) | indent(width=8) }}
{% endif %}
