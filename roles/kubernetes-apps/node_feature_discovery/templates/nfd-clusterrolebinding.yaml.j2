apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: node-feature-discovery
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: node-feature-discovery
subjects:
- kind: ServiceAccount
  name: node-feature-discovery
  namespace: {{ node_feature_discovery_namespace }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: node-feature-discovery-gc
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: node-feature-discovery-gc
subjects:
- kind: ServiceAccount
  name: {{ node_feature_discovery_gc_sa_name }}
  namespace: {{ node_feature_discovery_namespace }}
