---
- name: External vSphere Cloud Controller | Check vsphere credentials
  include_tasks: vsphere-credentials-check.yml

- name: External vSphere Cloud Controller | Generate CPI cloud-config
  template:
    src: "{{ item }}.j2"
    dest: "{{ kube_config_dir }}/{{ item }}"
    mode: 0640
  with_items:
    - external-vsphere-cpi-cloud-config
  when: inventory_hostname == groups['kube_control_plane'][0]

- name: External vSphere Cloud Controller | Generate Manifests
  template:
    src: "{{ item }}.j2"
    dest: "{{ kube_config_dir }}/{{ item }}"
    mode: 0644
  with_items:
    - external-vsphere-cpi-cloud-config-secret.yml
    - external-vsphere-cloud-controller-manager-roles.yml
    - external-vsphere-cloud-controller-manager-role-bindings.yml
    - external-vsphere-cloud-controller-manager-ds.yml
  register: external_vsphere_manifests
  when: inventory_hostname == groups['kube_control_plane'][0]

- name: External vSphere Cloud Provider Interface | Create a CPI configMap manifest
  command: "{{ bin_dir }}/kubectl create configmap cloud-config --from-file=vsphere.conf={{ kube_config_dir }}/external-vsphere-cpi-cloud-config -n kube-system --dry-run --save-config -o yaml"
  register: external_vsphere_configmap_manifest
  when: inventory_hostname == groups['kube_control_plane'][0]

- name: External vSphere Cloud Provider Interface | Apply a CPI configMap manifest
  command:
    cmd: "{{ bin_dir }}/kubectl apply -f -"
    stdin: "{{ external_vsphere_configmap_manifest.stdout }}"
  when: inventory_hostname == groups['kube_control_plane'][0]

- name: External vSphere Cloud Controller | Apply Manifests
  kube:
    kubectl: "{{ bin_dir }}/kubectl"
    filename: "{{ kube_config_dir }}/{{ item.item }}"
    state: "latest"
  with_items:
    - "{{ external_vsphere_manifests.results }}"
  when:
    - inventory_hostname == groups['kube_control_plane'][0]
    - not item is skipped
  loop_control:
    label: "{{ item.item }}"
