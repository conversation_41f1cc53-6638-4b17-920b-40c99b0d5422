---
external_vsphere_vcenter_port: "443"
external_vsphere_insecure: "true"

## A dictionary of extra arguments to add to the vsphere cloud controller manager daemonset
## Format:
##  external_vsphere_cloud_controller_extra_args:
##    arg1: "value1"
##    arg2: "value2"
external_vsphere_cloud_controller_extra_args: {}
external_vsphere_cloud_controller_image_tag: "latest"

external_vsphere_user: "{{ lookup('env', 'VSPHERE_USER') }}"
external_vsphere_password: "{{ lookup('env', 'VSPHERE_PASSWORD') }}"
