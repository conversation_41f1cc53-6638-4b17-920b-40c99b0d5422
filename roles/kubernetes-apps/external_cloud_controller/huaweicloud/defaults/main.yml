---
# The external cloud controller will need credentials to access
# openstack apis. Per default these values will be
# read from the environment.
external_huaweicloud_auth_url: "{{ lookup('env','OS_AUTH_URL')  }}"
external_huaweicloud_access_key: "{{ lookup('env','OS_ACCESS_KEY')  }}"
external_huaweicloud_secret_key: "{{ lookup('env','OS_SECRET_KEY')  }}"
external_huaweicloud_region: "{{ lookup('env','OS_REGION_NAME')  }}"
external_huaweicloud_project_id: "{{ lookup('env','OS_TENANT_ID')| default(lookup('env','OS_PROJECT_ID'),true) }}"
external_huaweicloud_cloud: "{{ lookup('env','OS_CLOUD') }}"

## A dictionary of extra arguments to add to the huawei cloud controller manager deployment
## Format:
##  external_huawei_cloud_controller_extra_args:
##    arg1: "value1"
##    arg2: "value2"
external_huawei_cloud_controller_extra_args: {}
external_huawei_cloud_controller_image_repo: "swr.ap-southeast-1.myhuaweicloud.com"
external_huawei_cloud_controller_image_tag: "v0.26.8"
