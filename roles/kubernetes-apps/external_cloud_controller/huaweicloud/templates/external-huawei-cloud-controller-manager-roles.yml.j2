apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: system:cloud-controller-manager
rules:
  - resources:
      - tokenreviews
    verbs:
      - get
      - list
      - watch
      - create
      - update
      - patch
    apiGroups:
      - authentication.k8s.io
  - resources:
      - configmaps
      - endpoints
      - pods
      - services
      - secrets
      - serviceaccounts
      - serviceaccounts/token
    verbs:
      - get
      - list
      - watch
      - create
      - update
      - patch
    apiGroups:
      - ''
  - resources:
      - nodes
    verbs:
      - get
      - list
      - watch
      - delete
      - patch
      - update
    apiGroups:
      - ''
  - resources:
      - services/status
      - pods/status
    verbs:
      - update
      - patch
    apiGroups:
      - ''
  - resources:
      - nodes/status
    verbs:
      - patch
      - update
    apiGroups:
      - ''
  - resources:
      - events
      - endpoints
    verbs:
      - create
      - patch
      - update
    apiGroups:
      - ''
  - resources:
      - leases
    verbs:
      - get
      - update
      - create
      - delete
    apiGroups:
      - coordination.k8s.io
  - resources:
      - customresourcedefinitions
    verbs:
      - get
      - update
      - create
      - delete
    apiGroups:
      - apiextensions.k8s.io
  - resources:
      - ingresses
    verbs:
      - get
      - list
      - watch
      - update
      - create
      - patch
      - delete
    apiGroups:
      - networking.k8s.io
  - resources:
      - ingresses/status
    verbs:
      - update
      - patch
    apiGroups:
      - networking.k8s.io
  - resources:
      - endpointslices
    verbs:
      - get
      - list
      - watch
    apiGroups:
      - discovery.k8s.io