---
- name: External Hcloud Cloud Controller | Generate Manifests
  template:
    src: "{{ item.file }}.j2"
    dest: "{{ kube_config_dir }}/{{ item.file }}"
    group: "{{ kube_cert_group }}"
    mode: 0640
  with_items:
    - {name: external-hcloud-cloud-secret, file: external-hcloud-cloud-secret.yml}
    - {name: external-hcloud-cloud-service-account, file: external-hcloud-cloud-service-account.yml}
    - {name: external-hcloud-cloud-role-bindings, file: external-hcloud-cloud-role-bindings.yml}
    - {name: "{{ 'external-hcloud-cloud-controller-manager-ds-with-networks' if external_hcloud_cloud.with_networks else 'external-hcloud-cloud-controller-manager-ds' }}", file: "{{ 'external-hcloud-cloud-controller-manager-ds-with-networks.yml' if external_hcloud_cloud.with_networks else 'external-hcloud-cloud-controller-manager-ds.yml' }}"}

  register: external_hcloud_manifests
  when: inventory_hostname == groups['kube_control_plane'][0]
  tags: external-hcloud

- name: External Hcloud Cloud Controller | Apply Manifests
  kube:
    kubectl: "{{ bin_dir }}/kubectl"
    filename: "{{ kube_config_dir }}/{{ item.item.file }}"
    state: "latest"
  with_items:
    - "{{ external_hcloud_manifests.results }}"
  when:
    - inventory_hostname == groups['kube_control_plane'][0]
    - not item is skipped
  loop_control:
    label: "{{ item.item.file }}"
  tags: external-hcloud
