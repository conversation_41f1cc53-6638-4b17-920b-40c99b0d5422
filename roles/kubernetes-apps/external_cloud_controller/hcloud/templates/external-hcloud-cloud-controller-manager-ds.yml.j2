---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: hcloud-cloud-controller-manager
  namespace: kube-system
  labels:
    k8s-app: hcloud-cloud-controller-manger
spec:
  selector:
    matchLabels:
      app: hcloud-cloud-controller-manager
  updateStrategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: hcloud-cloud-controller-manager
      annotations:
        scheduler.alpha.kubernetes.io/critical-pod: ''
    spec:
      serviceAccountName: {{ external_hcloud_cloud.service_account_name }}
      dnsPolicy: Default
      tolerations:
        - key: "node.cloudprovider.kubernetes.io/uninitialized"
          value: "true"
          effect: "NoSchedule"
        - key: "CriticalAddonsOnly"
          operator: "Exists"
        - key: "node-role.kubernetes.io/control-plane"
          effect: NoSchedule
        - key: "node.kubernetes.io/not-ready"
          effect: "NoSchedule"
      containers:
        - image: {{ docker_image_repo }}/hetznercloud/hcloud-cloud-controller-manager:{{ external_hcloud_cloud.controller_image_tag }}
          name: hcloud-cloud-controller-manager
          command:
            - "/bin/hcloud-cloud-controller-manager"
            - "--cloud-provider=hcloud"
            - "--leader-elect=false"
            - "--allow-untagged-cloud"
{% if external_hcloud_cloud.controller_extra_args is defined %}
          args:
{% for key, value in external_hcloud_cloud.controller_extra_args.items() %}
            - "{{ '--' + key + '=' + value }}"
{% endfor %}
{% endif %}
          resources:
            requests:
              cpu: 100m
              memory: 50Mi
          env:
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: HCLOUD_TOKEN
              valueFrom:
                secretKeyRef:
                  name: {{ external_hcloud_cloud.token_secret_name }}
                  key: token
{% if external_hcloud_cloud.network_name is defined %}
            - name: HCLOUD_NETWORK
              valueFrom:
                secretKeyRef:
                  name: {{ external_hcloud_cloud.token_secret_name }}
                  key: network
{% endif %}
{% if external_hcloud_cloud.network_routes_enabled is defined %}
            - name: HCLOUD_NETWORK_ROUTES_ENABLED
              value: "{{ external_hcloud_cloud.network_routes_enabled }}"
{% endif %}
{% if external_hcloud_cloud.load_balancers_location is defined %}
            - name: HCLOUD_LOAD_BALANCERS_LOCATION
              value: "{{ external_hcloud_cloud.load_balancers_location }}"
{% endif %}
{% if external_hcloud_cloud.load_balancers_network_zone is defined %}
            - name: HCLOUD_LOAD_BALANCERS_NETWORK_ZONE
              value: "{{ external_hcloud_cloud.load_balancers_network_zone }}"
{% endif %}
{% if external_hcloud_cloud.load_balancers_disable_private_ingress is defined %}
            - name: HCLOUD_LOAD_BALANCERS_DISABLE_PRIVATE_INGRESS
              value: "{{ external_hcloud_cloud.load_balancers_disable_private_ingress }}"
{% endif %}
{% if external_hcloud_cloud.load_balancers_use_private_ip is defined %}
            - name: HCLOUD_LOAD_BALANCERS_USE_PRIVATE_IP
              value: "{{ external_hcloud_cloud.load_balancers_use_private_ip }}"
{% endif %}
{% if external_hcloud_cloud.load_balancers_enabled is defined %}
            - name: HCLOUD_LOAD_BALANCERS_ENABLED
              value: "{{ external_hcloud_cloud.load_balancers_enabled }}"
{% endif %}
