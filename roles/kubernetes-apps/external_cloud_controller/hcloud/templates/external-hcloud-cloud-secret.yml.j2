---
apiVersion: v1
kind: Secret
metadata:
  name: "{{ external_hcloud_cloud.token_secret_name }}"
  namespace: kube-system
data:
  token: "{{ external_hcloud_cloud.hcloud_api_token | b64encode }}"
{% if external_hcloud_cloud.with_networks or external_hcloud_cloud.network_name is defined %}
{% if network_id is defined%}
  network: "{{ network_id | b64encode }}"
{% else %}
  network: "{{ external_hcloud_cloud.network_name | b64encode }}"
{% endif %}
{% endif %}
