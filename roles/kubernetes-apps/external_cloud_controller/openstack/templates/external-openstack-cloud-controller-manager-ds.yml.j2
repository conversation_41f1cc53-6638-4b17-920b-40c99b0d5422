---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: cloud-controller-manager
  namespace: kube-system
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: openstack-cloud-controller-manager
  namespace: kube-system
  labels:
    k8s-app: openstack-cloud-controller-manager
spec:
  selector:
    matchLabels:
      k8s-app: openstack-cloud-controller-manager
  updateStrategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        k8s-app: openstack-cloud-controller-manager
    spec:
      nodeSelector:
        node-role.kubernetes.io/control-plane: ""
      securityContext:
        runAsUser: 999
      tolerations:
      - key: node.cloudprovider.kubernetes.io/uninitialized
        value: "true"
        effect: NoSchedule
      - key: node-role.kubernetes.io/control-plane
        effect: NoSchedule
      serviceAccountName: cloud-controller-manager
      containers:
        - name: openstack-cloud-controller-manager
          image: {{ external_openstack_cloud_controller_image_repo }}:{{ external_openstack_cloud_controller_image_tag }}
          args:
            - /bin/openstack-cloud-controller-manager
            - --v=1
            - --cloud-config=$(CLOUD_CONFIG)
            - --cloud-provider=openstack
            - --cluster-name={{ cluster_name }}
            - --use-service-account-credentials=true
            - --bind-address={{ external_openstack_cloud_controller_bind_address }}
{% for key, value in external_openstack_cloud_controller_extra_args.items() %}
            - "{{ '--' + key + '=' + value }}"
{% endfor %}
          volumeMounts:
            - mountPath: /etc/kubernetes/pki
              name: k8s-certs
              readOnly: true
            - mountPath: /etc/ssl/certs
              name: ca-certs
              readOnly: true
{% if ssl_ca_dirs | length %}
{% for dir in ssl_ca_dirs %}
            - name: {{ dir | regex_replace('^/(.*)$', '\\1' ) | regex_replace('/', '-') }}
              mountPath: {{ dir }}
              readOnly: true
{% endfor %}
{% endif %}
            - mountPath: /etc/config/cloud.conf
              name: cloud-config-volume
              readOnly: true
              subPath: cloud.conf
            - mountPath: {{ kube_config_dir }}/external-openstack-cacert.pem
              name: cloud-config-volume
              readOnly: true
              subPath: ca.cert
{% if kubelet_flexvolumes_plugins_dir is defined %}
            - mountPath: /usr/libexec/kubernetes/kubelet-plugins/volume/exec
              name: flexvolume-dir
{% endif %}
          resources:
            requests:
              cpu: 200m
          env:
            - name: CLOUD_CONFIG
              value: /etc/config/cloud.conf
      hostNetwork: true
{% if external_openstack_cloud_controller_dns_policy is defined %}
      dnsPolicy: {{ external_openstack_cloud_controller_dns_policy }}
{% endif %}
      volumes:
{% if kubelet_flexvolumes_plugins_dir is defined %}
      - name: flexvolume-dir
        hostPath:
          path: "{{ kubelet_flexvolumes_plugins_dir }}"
          type: DirectoryOrCreate
{% endif %}
      - name: k8s-certs
        hostPath:
          path: /etc/kubernetes/pki
          type: DirectoryOrCreate
      - name: ca-certs
        hostPath:
          path: /etc/ssl/certs
          type: DirectoryOrCreate
{% if ssl_ca_dirs | length %}
{% for dir in ssl_ca_dirs %}
      - name: {{ dir | regex_replace('^/(.*)$', '\\1' ) | regex_replace('/', '-') }}
        hostPath:
          path: {{ dir }}
          type: DirectoryOrCreate
{% endfor %}
{% endif %}
      - name: cloud-config-volume
        secret:
          secretName: external-openstack-cloud-config
