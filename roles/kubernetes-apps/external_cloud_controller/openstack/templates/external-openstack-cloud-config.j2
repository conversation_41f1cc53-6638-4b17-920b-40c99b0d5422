[Global]
auth-url="{{ external_openstack_auth_url }}"
{% if external_openstack_application_credential_id == "" and external_openstack_application_credential_name == "" %}
username="{{ external_openstack_username }}"
password="{{ external_openstack_password }}"
{% endif %}
{% if external_openstack_application_credential_id is defined and external_openstack_application_credential_id != "" %}
application-credential-id={{ external_openstack_application_credential_id }}
{% endif %}
{% if external_openstack_application_credential_name is defined and external_openstack_application_credential_name != "" %}
application-credential-name={{ external_openstack_application_credential_name }}
{% endif %}
{% if external_openstack_application_credential_secret is defined and external_openstack_application_credential_secret != "" %}
application-credential-secret={{ external_openstack_application_credential_secret }}
{% endif %}
region="{{ external_openstack_region }}"
{% if external_openstack_tenant_id is defined and external_openstack_tenant_id != "" %}
tenant-id="{{ external_openstack_tenant_id }}"
{% endif %}
{% if external_openstack_tenant_name is defined and external_openstack_tenant_name != "" %}
tenant-name="{{ external_openstack_tenant_name }}"
{% endif %}
{% if external_openstack_domain_name is defined and external_openstack_domain_name != "" %}
domain-name="{{ external_openstack_domain_name }}"
{% elif external_openstack_domain_id is defined and external_openstack_domain_id != "" %}
domain-id ="{{ external_openstack_domain_id }}"
{% endif %}
{% if external_openstack_cacert is defined and external_openstack_cacert != "" %}
ca-file="{{ kube_config_dir }}/external-openstack-cacert.pem"
{% endif %}

[LoadBalancer]
enabled={{ external_openstack_lbaas_enabled | string | lower }}
{% if external_openstack_lbaas_floating_network_id is defined %}
floating-network-id={{ external_openstack_lbaas_floating_network_id }}
{% endif %}
{% if external_openstack_lbaas_floating_subnet_id is defined %}
floating-subnet-id={{ external_openstack_lbaas_floating_subnet_id }}
{% endif %}
{% if external_openstack_lbaas_method is defined %}
lb-method={{ external_openstack_lbaas_method }}
{% endif %}
{% if external_openstack_lbaas_provider is defined %}
lb-provider={{ external_openstack_lbaas_provider }}
{% endif %}
{% if external_openstack_lbaas_subnet_id is defined %}
subnet-id={{ external_openstack_lbaas_subnet_id }}
{% endif %}
{% if external_openstack_lbaas_network_id is defined %}
network-id={{ external_openstack_lbaas_network_id }}
{% endif %}
{% if external_openstack_lbaas_manage_security_groups is defined %}
manage-security-groups={{ external_openstack_lbaas_manage_security_groups }}
{% endif %}
{% if external_openstack_lbaas_create_monitor is defined %}
create-monitor={{ external_openstack_lbaas_create_monitor }}
{% endif %}
{% if external_openstack_lbaas_monitor_delay is defined %}
monitor-delay={{ external_openstack_lbaas_monitor_delay }}
{% endif %}
{% if external_openstack_lbaas_monitor_max_retries is defined %}
monitor-max-retries={{ external_openstack_lbaas_monitor_max_retries }}
{% endif %}
{% if external_openstack_lbaas_monitor_timeout is defined %}
monitor-timeout={{ external_openstack_lbaas_monitor_timeout }}
{% endif %}
{% if external_openstack_lbaas_internal_lb is defined %}
internal-lb={{ external_openstack_lbaas_internal_lb }}
{% endif %}
{% if external_openstack_enable_ingress_hostname is defined %}
enable-ingress-hostname={{ external_openstack_enable_ingress_hostname | string | lower }}
{% endif %}
{% if external_openstack_ingress_hostname_suffix is defined %}
ingress-hostname-suffix={{ external_openstack_ingress_hostname_suffix | string | lower }}
{% endif %}
{% if external_openstack_max_shared_lb is defined %}
max-shared-lb={{ external_openstack_max_shared_lb }}
{% endif %}

[Networking]
ipv6-support-disabled={{ external_openstack_network_ipv6_disabled | string | lower }}
{% for network_name in external_openstack_network_internal_networks %}
internal-network-name="{{ network_name }}"
{% endfor %}
{% for network_name in external_openstack_network_public_networks %}
public-network-name="{{ network_name }}"
{% endfor %}

[Metadata]
{% if external_openstack_metadata_search_order is defined %}
search-order="{{ external_openstack_metadata_search_order }}"
{% endif %}
