---
# The external cloud controller will need credentials to access
# openstack apis. Per default these values will be
# read from the environment.
external_openstack_auth_url: "{{ lookup('env', 'OS_AUTH_URL') }}"
external_openstack_username: "{{ lookup('env', 'OS_USERNAME') }}"
external_openstack_password: "{{ lookup('env', 'OS_PASSWORD') }}"
external_openstack_application_credential_id: "{{ lookup('env', 'OS_APPLICATION_CREDENTIAL_ID') }}"
external_openstack_application_credential_name: "{{ lookup('env', 'OS_APPLICATION_CREDENTIAL_NAME') }}"
external_openstack_application_credential_secret: "{{ lookup('env', 'OS_APPLICATION_CREDENTIAL_SECRET') }}"
external_openstack_region: "{{ lookup('env', 'OS_REGION_NAME') }}"
external_openstack_tenant_id: "{{ lookup('env', 'OS_TENANT_ID') | default(lookup('env', 'OS_PROJECT_ID'), true) }}"
external_openstack_tenant_name: "{{ lookup('env', 'OS_TENANT_NAME') | default(lookup('env', 'OS_PROJECT_NAME'), true) }}"
external_openstack_domain_name: "{{ lookup('env', 'OS_USER_DOMAIN_NAME') }}"
external_openstack_domain_id: "{{ lookup('env', 'OS_USER_DOMAIN_ID') }}"
external_openstack_cacert: "{{ lookup('env', 'OS_CACERT') }}"

## A dictionary of extra arguments to add to the openstack cloud controller manager daemonset
## Format:
##  external_openstack_cloud_controller_extra_args:
##    arg1: "value1"
##    arg2: "value2"
external_openstack_cloud_controller_extra_args: {}
external_openstack_cloud_controller_image_tag: "v1.28.2"
external_openstack_cloud_controller_bind_address: 127.0.0.1
external_openstack_cloud_controller_dns_policy: ClusterFirst
