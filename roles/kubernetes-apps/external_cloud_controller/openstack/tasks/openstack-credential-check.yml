---
- name: External OpenStack Cloud Controller | check external_openstack_auth_url value
  fail:
    msg: "external_openstack_auth_url is missing"
  when: external_openstack_auth_url is not defined or not external_openstack_auth_url


- name: External OpenStack Cloud Controller | check external_openstack_username or external_openstack_application_credential_name value
  fail:
    msg: "you must either set external_openstack_username or external_openstack_application_credential_name"
  when:
    - external_openstack_username is not defined or not external_openstack_username
    - external_openstack_application_credential_name is not defined or not external_openstack_application_credential_name


- name: External OpenStack Cloud Controller | check external_openstack_application_credential_id value
  fail:
    msg: "external_openstack_application_credential_id is missing"
  when:
    - external_openstack_application_credential_name is defined
    - external_openstack_application_credential_name | length > 0
    - external_openstack_application_credential_id is not defined or not external_openstack_application_credential_id


- name: External OpenStack Cloud Controller | check external_openstack_application_credential_secret value
  fail:
    msg: "external_openstack_application_credential_secret is missing"
  when:
    - external_openstack_application_credential_name is defined
    - external_openstack_application_credential_name | length > 0
    - external_openstack_application_credential_secret is not defined or not external_openstack_application_credential_secret


- name: External OpenStack Cloud Controller | check external_openstack_password value
  fail:
    msg: "external_openstack_password is missing"
  when:
    - external_openstack_username is defined
    - external_openstack_username | length > 0
    - external_openstack_application_credential_name is not defined or not external_openstack_application_credential_name
    - external_openstack_application_credential_secret is not defined or not external_openstack_application_credential_secret
    - external_openstack_password is not defined or not external_openstack_password


- name: External OpenStack Cloud Controller | check external_openstack_region value
  fail:
    msg: "external_openstack_region is missing"
  when: external_openstack_region is not defined or not external_openstack_region


- name: External OpenStack Cloud Controller | check external_openstack_tenant_id value
  fail:
    msg: "one of external_openstack_tenant_id or external_openstack_tenant_name must be specified"
  when:
    - external_openstack_tenant_id is not defined or not external_openstack_tenant_id
    - external_openstack_tenant_name is not defined or not external_openstack_tenant_name
    - external_openstack_application_credential_name is not defined or not external_openstack_application_credential_name


- name: External OpenStack Cloud Controller | check external_openstack_domain_id value
  fail:
    msg: "one of external_openstack_domain_id or external_openstack_domain_name must be specified"
  when:
    - external_openstack_domain_id is not defined or not external_openstack_domain_id
    - external_openstack_domain_name is not defined or not external_openstack_domain_name
    - external_openstack_application_credential_name is not defined or not external_openstack_application_credential_name
