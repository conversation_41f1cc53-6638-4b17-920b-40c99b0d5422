---
dependencies:
  - role: kubernetes-apps/external_cloud_controller/openstack
    when:
      - cloud_provider is defined
      - cloud_provider == "external"
      - external_cloud_provider is defined
      - external_cloud_provider == "openstack"
      - inventory_hostname == groups['kube_control_plane'][0]
    tags:
      - external-cloud-controller
      - external-openstack
  - role: kubernetes-apps/external_cloud_controller/vsphere
    when:
      - cloud_provider is defined
      - cloud_provider == "external"
      - external_cloud_provider is defined
      - external_cloud_provider == "vsphere"
      - inventory_hostname == groups['kube_control_plane'][0]
    tags:
      - external-cloud-controller
      - external-vsphere
  - role: kubernetes-apps/external_cloud_controller/hcloud
    when:
      - cloud_provider is defined
      - cloud_provider == "external"
      - external_cloud_provider is defined
      - external_cloud_provider == "hcloud"
      - inventory_hostname == groups['kube_control_plane'][0]
    tags:
      - external-cloud-controller
      - external-hcloud
  - role: kubernetes-apps/external_cloud_controller/huaweicloud
    when:
      - cloud_provider is defined
      - cloud_provider == "external"
      - external_cloud_provider is defined
      - external_cloud_provider == "huaweicloud"
      - inventory_hostname == groups['kube_control_plane'][0]
    tags:
      - external-cloud-controller
      - external-huaweicloud
