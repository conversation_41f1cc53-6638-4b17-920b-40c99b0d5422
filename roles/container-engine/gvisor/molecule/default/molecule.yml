---
role_name_check: 1
driver:
  name: vagrant
  provider:
    name: libvirt
platforms:
  - name: ubuntu20
    box: generic/ubuntu2004
    cpus: 1
    memory: 1024
    nested: true
    groups:
      - kube_control_plane
    provider_options:
      driver: kvm
  - name: almalinux8
    box: almalinux/8
    cpus: 1
    memory: 1024
    nested: true
    groups:
      - kube_control_plane
    provider_options:
      driver: kvm
provisioner:
  name: ansible
  env:
    ANSIBLE_ROLES_PATH: ../../../../
  config_options:
    defaults:
      callbacks_enabled: profile_tasks
      timeout: 120
  inventory:
    group_vars:
      all:
        become: true
verifier:
  name: testinfra
