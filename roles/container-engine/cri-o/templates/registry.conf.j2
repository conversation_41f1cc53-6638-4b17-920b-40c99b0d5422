[[registry]]
prefix = "{{ item.prefix | default(item.location) }}"
insecure = {{ item.insecure | default('false') | string | lower }}
blocked = {{ item.blocked | default('false') | string | lower }}
location = "{{ item.location }}"
{% if item.mirrors is defined %}
{% for mirror in item.mirrors %}

[[registry.mirror]]
location = "{{ mirror.location }}"
insecure = {{ mirror.insecure | default('false') | string | lower }}
{% endfor %}
{% endif %}
