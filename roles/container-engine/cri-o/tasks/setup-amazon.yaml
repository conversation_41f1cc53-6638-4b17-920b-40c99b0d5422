---
- name: Check that amzn2-extras.repo exists
  stat:
    path: /etc/yum.repos.d/amzn2-extras.repo
  register: amzn2_extras_file_stat

- name: Find docker repo in amzn2-extras.repo file
  lineinfile:
    dest: /etc/yum.repos.d/amzn2-extras.repo
    line: "[amzn2extra-docker]"
  check_mode: yes
  register: amzn2_extras_docker_repo
  when:
    - amzn2_extras_file_stat.stat.exists

- name: Remove docker repository
  community.general.ini_file:
    dest: /etc/yum.repos.d/amzn2-extras.repo
    section: amzn2extra-docker
    option: enabled
    value: "0"
    backup: yes
    mode: 0644
  when:
    - amzn2_extras_file_stat.stat.exists
    - not amzn2_extras_docker_repo.changed
