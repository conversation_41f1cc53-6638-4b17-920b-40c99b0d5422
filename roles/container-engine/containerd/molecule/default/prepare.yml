---
- name: Prepare
  hosts: all
  gather_facts: False
  become: true
  vars:
    ignore_assert_errors: true
  roles:
    - role: kubespray-defaults
    - role: bootstrap-os
    - role: kubernetes/preinstall
    - role: adduser
      user: "{{ addusers.kube }}"
  tasks:
    - name: Download CNI
      include_tasks: "../../../../download/tasks/download_file.yml"
      vars:
        download: "{{ download_defaults | combine(downloads.cni) }}"

- name: Prepare CNI
  hosts: all
  gather_facts: False
  become: true
  vars:
    ignore_assert_errors: true
    kube_network_plugin: cni
  roles:
    - role: kubespray-defaults
    - role: network_plugin/cni
