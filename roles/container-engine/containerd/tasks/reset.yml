---
- name: <PERSON><PERSON><PERSON> | Remove containerd repository for RedHat os family
  file:
    path: "{{ yum_repo_dir }}/containerd.repo"
    state: absent
  when:
    - ansible_os_family in ['RedHat']
  tags:
    - reset_containerd

- name: Containerd | Remove containerd repository for Debian os family
  apt_repository:
    repo: "{{ item }}"
    state: absent
  with_items: "{{ containerd_repo_info.repos }}"
  when: ansible_pkg_mgr == 'apt'
  tags:
    - reset_containerd

- name: Containerd | Stop containerd service
  service:
    name: containerd
    daemon_reload: true
    enabled: false
    state: stopped
  tags:
    - reset_containerd

- name: Containerd | Remove configuration files
  file:
    path: "{{ item }}"
    state: absent
  loop:
    - /etc/systemd/system/containerd.service
    - "{{ containerd_systemd_dir }}"
    - "{{ containerd_cfg_dir }}"
    - "{{ containerd_storage_dir }}"
    - "{{ containerd_state_dir }}"
  tags:
    - reset_containerd
