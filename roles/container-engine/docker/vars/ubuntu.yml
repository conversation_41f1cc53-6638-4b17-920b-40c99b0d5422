---
# containerd versions are only relevant for docker
containerd_versioned_pkg:
  'latest': "{{ containerd_package }}"
  '1.3.7': "{{ containerd_package }}=1.3.7-1"
  '1.3.9': "{{ containerd_package }}=1.3.9-1"
  '1.4.3': "{{ containerd_package }}=1.4.3-2"
  '1.4.4': "{{ containerd_package }}=1.4.4-1"
  '1.4.6': "{{ containerd_package }}=1.4.6-1"
  '1.4.9': "{{ containerd_package }}=1.4.9-1"
  '1.4.12': "{{ containerd_package }}=1.4.12-1"
  '1.6.4': "{{ containerd_package }}=1.6.4-1"
  '1.6.6': "{{ containerd_package }}=1.6.6-1"
  '1.6.7': "{{ containerd_package }}=1.6.7-1"
  '1.6.8': "{{ containerd_package }}=1.6.8-1"
  '1.6.9': "{{ containerd_package }}=1.6.9-1"
  '1.6.10': "{{ containerd_package }}=1.6.10-1"
  '1.6.11': "{{ containerd_package }}=1.6.11-1"
  '1.6.12': "{{ containerd_package }}=1.6.12-1"
  '1.6.13': "{{ containerd_package }}=1.6.13-1"
  '1.6.14': "{{ containerd_package }}=1.6.14-1"
  '1.6.15': "{{ containerd_package }}=1.6.15-1"
  '1.6.16': "{{ containerd_package }}=1.6.16-1"
  '1.6.18': "{{ containerd_package }}=1.6.18-1"
  '1.6.28': "{{ containerd_package }}=1.6.28-2"
  '1.6.31': "{{ containerd_package }}=1.6.31-1"
  'stable': "{{ containerd_package }}=1.6.31-1"
  'edge': "{{ containerd_package }}=1.6.31-1"

# https://download.docker.com/linux/ubuntu/
docker_versioned_pkg:
  'latest': docker-ce
  '18.09': docker-ce=5:18.09.9~3-0~ubuntu-{{ ansible_distribution_release | lower }}
  '19.03': docker-ce=5:19.03.15~3-0~ubuntu-{{ ansible_distribution_release | lower }}
  '20.10': docker-ce=5:20.10.20~3-0~ubuntu-{{ ansible_distribution_release | lower }}
  '23.0': docker-ce=5:23.0.6-1~ubuntu.{{ ansible_distribution_version }}~{{ ansible_distribution_release | lower }}
  '24.0': docker-ce=5:24.0.9-1~ubuntu.{{ ansible_distribution_version }}~{{ ansible_distribution_release | lower }}
  '26.0': docker-ce=5:26.0.2-1~ubuntu.{{ ansible_distribution_version }}~{{ ansible_distribution_release | lower }}
  '26.1': docker-ce=5:26.1.2-1~ubuntu.{{ ansible_distribution_version }}~{{ ansible_distribution_release | lower }}
  'stable': docker-ce=5:26.1.2-1~ubuntu.{{ ansible_distribution_version }}~{{ ansible_distribution_release | lower }}
  'edge': docker-ce=5:26.1.2-1~ubuntu.{{ ansible_distribution_version }}~{{ ansible_distribution_release | lower }}

docker_cli_versioned_pkg:
  'latest': docker-ce-cli
  '18.09': docker-ce-cli=5:18.09.9~3-0~ubuntu-{{ ansible_distribution_release | lower }}
  '19.03': docker-ce-cli=5:19.03.15~3-0~ubuntu-{{ ansible_distribution_release | lower }}
  '20.10': docker-ce-cli=5:20.10.20~3-0~ubuntu-{{ ansible_distribution_release | lower }}
  '23.0': docker-ce-cli=5:23.0.6-1~ubuntu.{{ ansible_distribution_version }}~{{ ansible_distribution_release | lower }}
  '24.0': docker-ce-cli=5:24.0.9-1~ubuntu.{{ ansible_distribution_version }}~{{ ansible_distribution_release | lower }}
  '26.0': docker-ce-cli=5:26.0.2-1~ubuntu.{{ ansible_distribution_version }}~{{ ansible_distribution_release | lower }}
  '26.1': docker-ce-cli=5:26.1.2-1~ubuntu.{{ ansible_distribution_version }}~{{ ansible_distribution_release | lower }}
  'stable': docker-ce-cli=5:24.0.9-1~ubuntu.{{ ansible_distribution_version }}~{{ ansible_distribution_release | lower }}
  'edge': docker-ce-cli=5:24.0.9-1~ubuntu.{{ ansible_distribution_version }}~{{ ansible_distribution_release | lower }}

docker_package_info:
  pkgs:
    - "{{ containerd_versioned_pkg[docker_containerd_version | string] }}"
    - "{{ docker_cli_versioned_pkg[docker_cli_version | string] }}"
    - "{{ docker_versioned_pkg[docker_version | string] }}"

docker_repo_key_info:
  url: '{{ docker_ubuntu_repo_gpgkey }}'
  repo_keys:
    - '{{ docker_ubuntu_repo_repokey }}'

docker_repo_info:
  repos:
    - >
      deb [arch={{ host_architecture }}] {{ docker_ubuntu_repo_base_url }}
      {{ ansible_distribution_release | lower }}
      stable
