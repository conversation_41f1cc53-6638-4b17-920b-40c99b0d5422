---
# containerd versions are only relevant for docker
containerd_versioned_pkg:
  'latest': "{{ containerd_package }}"
  '1.3.7': "{{ containerd_package }}-1.3.7-3.1.fc{{ ansible_distribution_major_version }}"
  '1.3.9': "{{ containerd_package }}-1.3.9-3.1.fc{{ ansible_distribution_major_version }}"
  '1.4.3': "{{ containerd_package }}-1.4.3-3.2.fc{{ ansible_distribution_major_version }}"
  '1.4.4': "{{ containerd_package }}-1.4.4-3.1.fc{{ ansible_distribution_major_version }}"
  '1.4.6': "{{ containerd_package }}-1.4.6-3.1.fc{{ ansible_distribution_major_version }}"
  '1.4.9': "{{ containerd_package }}-1.4.9-3.1.fc{{ ansible_distribution_major_version }}"
  '1.4.12': "{{ containerd_package }}-1.4.12-3.1.fc{{ ansible_distribution_major_version }}"
  '1.6.4': "{{ containerd_package }}-1.6.4-3.1.fc{{ ansible_distribution_major_version }}"
  '1.6.6': "{{ containerd_package }}-1.6.6-3.1.fc{{ ansible_distribution_major_version }}"
  '1.6.7': "{{ containerd_package }}-1.6.7-3.1.fc{{ ansible_distribution_major_version }}"
  '1.6.8': "{{ containerd_package }}-1.6.8-3.1.fc{{ ansible_distribution_major_version }}"
  '1.6.9': "{{ containerd_package }}-1.6.9-3.1.fc{{ ansible_distribution_major_version }}"
  '1.6.10': "{{ containerd_package }}-1.6.10-3.1.fc{{ ansible_distribution_major_version }}"
  '1.6.11': "{{ containerd_package }}-1.6.11-3.1.fc{{ ansible_distribution_major_version }}"
  '1.6.12': "{{ containerd_package }}-1.6.12-3.1.fc{{ ansible_distribution_major_version }}"
  '1.6.13': "{{ containerd_package }}-1.6.13-3.1.fc{{ ansible_distribution_major_version }}"
  '1.6.14': "{{ containerd_package }}-1.6.14-3.1.fc{{ ansible_distribution_major_version }}"
  '1.6.15': "{{ containerd_package }}-1.6.15-3.1.fc{{ ansible_distribution_major_version }}"
  '1.6.16': "{{ containerd_package }}-1.6.16-3.1.fc{{ ansible_distribution_major_version }}"
  '1.6.18': "{{ containerd_package }}-1.6.18-3.1.fc{{ ansible_distribution_major_version }}"
  '1.6.28': "{{ containerd_package }}-1.6.28-3.2.fc{{ ansible_distribution_major_version }}"
  '1.6.31': "{{ containerd_package }}-1.6.31-3.1.fc{{ ansible_distribution_major_version }}"
  'stable': "{{ containerd_package }}-1.6.31-3.1.fc{{ ansible_distribution_major_version }}"
  'edge': "{{ containerd_package }}-1.6.31-3.1.fc{{ ansible_distribution_major_version }}"

# https://docs.docker.com/install/linux/docker-ce/fedora/
# https://download.docker.com/linux/fedora/<fedora-version>/x86_64/stable/Packages/
docker_versioned_pkg:
  'latest': docker-ce
  '19.03': docker-ce-19.03.15-3.fc{{ ansible_distribution_major_version }}
  '20.10': docker-ce-20.10.20-3.fc{{ ansible_distribution_major_version }}
  '23.0': docker-ce-3:23.0.6-1.fc{{ ansible_distribution_major_version }}
  '24.0': docker-ce-3:24.0.9-1.fc{{ ansible_distribution_major_version }}
  '26.0': docker-ce-3:26.0.2-1.fc{{ ansible_distribution_major_version }}
  '26.1': docker-ce-3:26.1.2-1.fc{{ ansible_distribution_major_version }}
  'stable': docker-ce-3:26.1.2-1.fc{{ ansible_distribution_major_version }}
  'edge': docker-ce-3:26.1.2-1.fc{{ ansible_distribution_major_version }}

docker_cli_versioned_pkg:
  'latest': docker-ce-cli
  '19.03': docker-ce-cli-19.03.15-3.fc{{ ansible_distribution_major_version }}
  '20.10': docker-ce-cli-20.10.20-3.fc{{ ansible_distribution_major_version }}
  '23.0': docker-ce-cli-1:23.0.6-1.fc{{ ansible_distribution_major_version }}
  '24.0': docker-ce-cli-1:24.0.9-1.fc{{ ansible_distribution_major_version }}
  '26.0': docker-ce-cli-1:26.0.2-1.fc{{ ansible_distribution_major_version }}
  '26.1': docker-ce-cli-1:26.0.2-1.fc{{ ansible_distribution_major_version }}
  'stable': docker-ce-cli-1:26.0.2-1.fc{{ ansible_distribution_major_version }}
  'edge': docker-ce-cli-1:26.0.2-1.fc{{ ansible_distribution_major_version }}

docker_package_info:
  enablerepo: "docker-ce"
  pkgs:
    - "{{ containerd_versioned_pkg[docker_containerd_version | string] }}"
    - "{{ docker_cli_versioned_pkg[docker_cli_version | string] }}"
    - "{{ docker_versioned_pkg[docker_version | string] }}"
