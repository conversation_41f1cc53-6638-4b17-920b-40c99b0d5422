---

docker_version: 26.1
docker_cli_version: "{{ docker_version }}"
docker_rh_repo_base_url: 'https://download.docker.com/linux/centos/8/$basearch/stable'

# containerd versions are only relevant for docker
containerd_versioned_pkg:
  'latest': "{{ containerd_package }}"
  '1.3.7': "{{ containerd_package }}-1.3.7-3.1.el8"
  '1.3.9': "{{ containerd_package }}-1.3.9-3.1.el8"
  '1.4.3': "{{ containerd_package }}-1.4.3-3.2.el8"
  '1.4.4': "{{ containerd_package }}-1.4.4-3.1.el8"
  '1.4.6': "{{ containerd_package }}-1.4.6-3.1.el8"
  '1.4.9': "{{ containerd_package }}-1.4.9-3.1.el8"
  '1.4.12': "{{ containerd_package }}-1.4.12-3.1.el8"
  '1.6.4': "{{ containerd_package }}-1.6.4-3.1.el8"
  '1.6.6': "{{ containerd_package }}-1.6.6-3.1.el8"
  '1.6.7': "{{ containerd_package }}-1.6.7-3.1.el8"
  '1.6.8': "{{ containerd_package }}-1.6.8-3.1.el8"
  '1.6.9': "{{ containerd_package }}-1.6.9-3.1.el8"
  '1.6.10': "{{ containerd_package }}-1.6.10-3.1.el8"
  '1.6.11': "{{ containerd_package }}-1.6.11-3.1.el8"
  '1.6.12': "{{ containerd_package }}-1.6.12-3.1.el8"
  '1.6.13': "{{ containerd_package }}-1.6.13-3.1.el8"
  '1.6.14': "{{ containerd_package }}-1.6.14-3.1.el8"
  '1.6.15': "{{ containerd_package }}-1.6.15-3.1.el8"
  '1.6.16': "{{ containerd_package }}-1.6.16-3.1.el8"
  '1.6.18': "{{ containerd_package }}-1.6.18-3.1.el8"
  '1.6.28': "{{ containerd_package }}-1.6.28-3.1.el8"
  '1.6.31': "{{ containerd_package }}-1.6.31-3.1.el8"
  'stable': "{{ containerd_package }}-1.6.31-3.1.el8"
  'edge': "{{ containerd_package }}-1.6.31-3.1.el8"

# https://docs.docker.com/engine/installation/linux/centos/#install-from-a-package
# https://download.docker.com/linux/centos/8/x86_64/stable/Packages/
# or do 'yum --showduplicates list docker-engine'
docker_versioned_pkg:
  'latest': docker-ce
  '18.09': docker-ce-3:18.09.9-3.el8
  '19.03': docker-ce-3:19.03.15-3.el8
  '23.0': docker-ce-3:23.0.6-1.el8
  '24.0': docker-ce-3:24.0.9-1.el8
  '26.0': docker-ce-26.0.2-1.el8
  '26.1': docker-ce-26.1.2-1.el8
  'stable': docker-ce-26.1.2-1.el8
  'edge': docker-ce-26.1.2-1.el8

docker_cli_versioned_pkg:
  'latest': docker-ce-cli
  '18.09': docker-ce-cli-1:18.09.9-3.el8
  '19.03': docker-ce-cli-1:19.03.15-3.el8
  '23.0': docker-ce-cli-1:23.0.6-1.el8
  '24.0': docker-ce-cli-1:24.0.9-1.el8
  '26.0': docker-ce-cli-26.0.2-1.el8
  '26.1': docker-ce-cli-26.1.2-1.el8
  'stable': docker-ce-cli-26.1.2-1.el8
  'edge': docker-ce-cli-26.1.2-1.el8

docker_package_info:
  enablerepo: "docker-ce"
  pkgs:
    - "{{ containerd_versioned_pkg[docker_containerd_version | string] }}"
    - "{{ docker_cli_versioned_pkg[docker_cli_version | string] }}"
    - "{{ docker_versioned_pkg[docker_version | string] }}"
