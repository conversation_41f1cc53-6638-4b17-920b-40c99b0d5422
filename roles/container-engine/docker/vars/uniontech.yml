---
# containerd versions are only relevant for docker
containerd_versioned_pkg:
  'latest': "{{ containerd_package }}"
  '1.3.7': "{{ containerd_package }}-1.3.7-3.1.el{{ ansible_distribution_major_version }}"
  '1.3.9': "{{ containerd_package }}-1.3.9-3.1.el{{ ansible_distribution_major_version }}"
  '1.4.3': "{{ containerd_package }}-1.4.3-3.2.el{{ ansible_distribution_major_version }}"
  '1.4.4': "{{ containerd_package }}-1.4.4-3.1.el{{ ansible_distribution_major_version }}"
  '1.4.6': "{{ containerd_package }}-1.4.6-3.1.el{{ ansible_distribution_major_version }}"
  '1.4.9': "{{ containerd_package }}-1.4.9-3.1.el{{ ansible_distribution_major_version }}"
  '1.4.12': "{{ containerd_package }}-1.4.12-3.1.el{{ ansible_distribution_major_version }}"
  '1.6.4': "{{ containerd_package }}-1.6.4-3.1.el{{ ansible_distribution_major_version }}"
  '1.6.8': "{{ containerd_package }}-1.6.8-3.1.el{{ ansible_distribution_major_version }}"
  '1.6.9': "{{ containerd_package }}-1.6.9-3.1.el{{ ansible_distribution_major_version }}"
  '1.6.10': "{{ containerd_package }}-1.6.10-3.1.el{{ ansible_distribution_major_version }}"
  '1.6.11': "{{ containerd_package }}-1.6.11-3.1.el{{ ansible_distribution_major_version }}"
  '1.6.12': "{{ containerd_package }}-1.6.12-3.1.el{{ ansible_distribution_major_version }}"
  '1.6.13': "{{ containerd_package }}-1.6.13-3.1.el{{ ansible_distribution_major_version }}"
  '1.6.14': "{{ containerd_package }}-1.6.14-3.1.el{{ ansible_distribution_major_version }}"
  '1.6.15': "{{ containerd_package }}-1.6.15-3.1.el{{ ansible_distribution_major_version }}"
  '1.6.16': "{{ containerd_package }}-1.6.16-3.1.el{{ ansible_distribution_major_version }}"
  '1.6.18': "{{ containerd_package }}-1.6.18-3.1.el{{ ansible_distribution_major_version }}"
  '1.6.28': "{{ containerd_package }}-1.6.28-3.1.el{{ ansible_distribution_major_version }}"
  'stable': "{{ containerd_package }}-1.6.28-3.1.el{{ ansible_distribution_major_version }}"
  'edge': "{{ containerd_package }}-1.6.28-3.1.el{{ ansible_distribution_major_version }}"

docker_version: 19.03
docker_cli_version: 19.03

# https://docs.docker.com/engine/installation/linux/centos/#install-from-a-package
# https://download.docker.com/linux/centos/<centos_version>>/x86_64/stable/Packages/
# or do 'yum --showduplicates list docker-engine'
docker_versioned_pkg:
  'latest': docker-ce
  '18.09': docker-ce-3:18.09.9-3.el7
  '19.03': docker-ce-3:19.03.15-3.el{{ ansible_distribution_major_version }}
  '20.10': docker-ce-3:20.10.17-3.el{{ ansible_distribution_major_version }}
  '23.0': docker-ce-3:23.0.6-1.el{{ ansible_distribution_major_version }}
  '24.0': docker-ce-3:24.0.9-1.el{{ ansible_distribution_major_version }}
  'stable': docker-ce-3:24.0.9-1.el{{ ansible_distribution_major_version }}
  'edge': docker-ce-3:24.0.9-1.el{{ ansible_distribution_major_version }}

docker_cli_versioned_pkg:
  'latest': docker-ce-cli
  '18.09': docker-ce-cli-1:18.09.9-3.el7
  '19.03': docker-ce-cli-1:19.03.15-3.el{{ ansible_distribution_major_version }}
  '20.10': docker-ce-cli-1:20.10.17-3.el{{ ansible_distribution_major_version }}
  '23.0': docker-ce-cli-1:23.0.6-1.el{{ ansible_distribution_major_version }}
  '24.0': docker-ce-cli-1:24.0.9-1.el{{ ansible_distribution_major_version }}
  'stable': docker-ce-cli-1:24.0.9-1.el{{ ansible_distribution_major_version }}
  'edge': docker-ce-cli-1:24.0.9-1.el{{ ansible_distribution_major_version }}

docker_package_info:
  enablerepo: "docker-ce"
  disablerepo: "UniontechOS-20-AppStream"
  pkgs:
    - "{{ containerd_versioned_pkg[docker_containerd_version | string] }}"
    - "{{ docker_cli_versioned_pkg[docker_cli_version | string] }}"
    - "{{ docker_versioned_pkg[docker_version | string] }}"
