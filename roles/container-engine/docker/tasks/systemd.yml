---
- name: Create docker service systemd directory if it doesn't exist
  file:
    path: /etc/systemd/system/docker.service.d
    state: directory
    mode: 0755

- name: Write docker proxy drop-in
  template:
    src: http-proxy.conf.j2
    dest: /etc/systemd/system/docker.service.d/http-proxy.conf
    mode: 0644
  notify: Restart docker
  when: http_proxy is defined or https_proxy is defined

- name: Get systemd version
  # noqa command-instead-of-module - systemctl is called intentionally here
  shell: set -o pipefail && systemctl --version | head -n 1 | cut -d " " -f 2
  args:
    executable: /bin/bash
  register: systemd_version
  when: not is_ostree
  changed_when: false
  check_mode: false

- name: Write docker.service systemd file
  template:
    src: docker.service.j2
    dest: /etc/systemd/system/docker.service
    mode: 0644
  register: docker_service_file
  notify: Restart docker
  when:
    - not ansible_os_family in ["Flatcar", "Flatcar Container Linux by Kinvolk"]
    - not is_fedora_coreos

- name: Write docker options systemd drop-in
  template:
    src: docker-options.conf.j2
    dest: "/etc/systemd/system/docker.service.d/docker-options.conf"
    mode: 0644
  notify: Restart docker

- name: Write docker dns systemd drop-in
  template:
    src: docker-dns.conf.j2
    dest: "/etc/systemd/system/docker.service.d/docker-dns.conf"
    mode: 0644
  notify: Restart docker
  when: dns_mode != 'none' and resolvconf_mode == 'docker_dns'

- name: Copy docker orphan clean up script to the node
  copy:
    src: cleanup-docker-orphans.sh
    dest: "{{ bin_dir }}/cleanup-docker-orphans.sh"
    mode: 0755
  when: docker_orphan_clean_up | bool

- name: Write docker orphan clean up systemd drop-in
  template:
    src: docker-orphan-cleanup.conf.j2
    dest: "/etc/systemd/system/docker.service.d/docker-orphan-cleanup.conf"
    mode: 0644
  notify: Restart docker
  when: docker_orphan_clean_up | bool

- name: Flush handlers
  meta: flush_handlers
