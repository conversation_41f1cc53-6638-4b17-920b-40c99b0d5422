---
- name: Check if fedora coreos
  stat:
    path: /run/ostree-booted
    get_attributes: no
    get_checksum: no
    get_mime: no
  register: ostree

- name: Set is_ostree
  set_fact:
    is_ostree: "{{ ostree.stat.exists }}"

- name: Gather os specific variables
  include_vars: "{{ item }}"
  with_first_found:
    - files:
        - "{{ ansible_distribution | lower }}-{{ ansible_distribution_version | lower | replace('/', '_') }}.yml"
        - "{{ ansible_distribution | lower }}-{{ ansible_distribution_release | lower }}-{{ host_architecture }}.yml"
        - "{{ ansible_distribution | lower }}-{{ ansible_distribution_release | lower }}.yml"
        - "{{ ansible_distribution | lower }}-{{ ansible_distribution_major_version | lower | replace('/', '_') }}.yml"
        - "{{ ansible_distribution | lower }}-{{ host_architecture }}.yml"
        - "{{ ansible_distribution | lower }}.yml"
        - "{{ ansible_distribution.split(' ')[0] | lower }}.yml"
        - "{{ ansible_os_family | lower }}-{{ ansible_distribution_major_version | lower | replace('/', '_') }}.yml"
        - "{{ ansible_os_family | lower }}-{{ host_architecture }}.yml"
        - "{{ ansible_os_family | lower }}.yml"
        - defaults.yml
      paths:
        - ../vars
      skip: true
  tags:
    - facts

- name: Warn about Docker version on SUSE
  debug:
    msg: "SUSE distributions always install Docker from the distro repos"
  when: ansible_pkg_mgr == 'zypper'

- name: Gather DNS facts
  include_tasks: set_facts_dns.yml
  when: dns_mode != 'none' and resolvconf_mode == 'docker_dns'
  tags:
    - facts

- name: Pre-upgrade docker
  import_tasks: pre-upgrade.yml

- name: Ensure docker-ce repository public key is installed
  apt_key:
    id: "{{ item }}"
    url: "{{ docker_repo_key_info.url }}"
    keyring: "{{ docker_repo_key_keyring|default(omit) }}"
    state: present
  register: keyserver_task_result
  until: keyserver_task_result is succeeded
  retries: 4
  delay: "{{ retry_stagger | d(3) }}"
  with_items: "{{ docker_repo_key_info.repo_keys }}"
  environment: "{{ proxy_env }}"
  when: ansible_pkg_mgr == 'apt'

# ref to https://github.com/kubernetes-sigs/kubespray/issues/11086
- name: Remove the archived debian apt repository
  lineinfile:
    path: /etc/apt/sources.list
    regexp: 'buster-backports'
    state: absent
    backup: yes
  when:
    - ansible_os_family == 'Debian'
    - ansible_distribution_release == "buster"

- name: Ensure docker-ce repository is enabled
  apt_repository:
    repo: "{{ item }}"
    state: present
  with_items: "{{ docker_repo_info.repos }}"
  when: ansible_pkg_mgr == 'apt'

- name: Configure docker repository on Fedora
  template:
    src: "fedora_docker.repo.j2"
    dest: "{{ yum_repo_dir }}/docker.repo"
    mode: 0644
  when: ansible_distribution == "Fedora" and not is_ostree

- name: Configure docker repository on RedHat/CentOS/OracleLinux/AlmaLinux/KylinLinux
  template:
    src: "rh_docker.repo.j2"
    dest: "{{ yum_repo_dir }}/docker-ce.repo"
    mode: 0644
  when:
    - ansible_os_family == "RedHat"
    - ansible_distribution != "Fedora"
    - not is_ostree

- name: Remove dpkg hold
  dpkg_selections:
    name: "{{ item }}"
    selection: install
  when: ansible_pkg_mgr == 'apt'
  register: ret
  changed_when: false
  failed_when:
    - ret is failed
    - ret.msg != ( "Failed to find package '" + item + "' to perform selection 'install'." )
  with_items:
    - "{{ containerd_package }}"
    - docker-ce
    - docker-ce-cli

- name: Ensure docker packages are installed
  package:
    name: "{{ docker_package_info.pkgs }}"
    state: "{{ docker_package_info.state | default('present') }}"
  module_defaults:
    apt:
      update_cache: true
    dnf:
      enablerepo: "{{ docker_package_info.enablerepo | default(omit) }}"
      disablerepo: "{{ docker_package_info.disablerepo | default(omit) }}"
    yum:
      enablerepo: "{{ docker_package_info.enablerepo | default(omit) }}"
    zypper:
      update_cache: true
  register: docker_task_result
  until: docker_task_result is succeeded
  retries: 4
  delay: "{{ retry_stagger | d(3) }}"
  notify: Restart docker
  when:
    - not ansible_os_family in ["Flatcar", "Flatcar Container Linux by Kinvolk"]
    - not is_ostree
    - docker_package_info.pkgs | length > 0

# This is required to ensure any apt upgrade will not break kubernetes
- name: Tell Debian hosts not to change the docker version with apt upgrade
  dpkg_selections:
    name: "{{ item }}"
    selection: hold
  when: ansible_pkg_mgr == 'apt'
  changed_when: false
  with_items:
    - "{{ containerd_package }}"
    - docker-ce
    - docker-ce-cli

- name: Ensure docker started, remove our config if docker start failed and try again
  block:
    - name: Ensure service is started if docker packages are already present
      service:
        name: docker
        state: started
      when: docker_task_result is not changed
  rescue:
    - debug:  # noqa name[missing]
        msg: "Docker start failed. Try to remove our config"
    - name: Remove kubespray generated config
      file:
        path: "{{ item }}"
        state: absent
      with_items:
        - /etc/systemd/system/docker.service.d/http-proxy.conf
        - /etc/systemd/system/docker.service.d/docker-options.conf
        - /etc/systemd/system/docker.service.d/docker-dns.conf
        - /etc/systemd/system/docker.service.d/docker-orphan-cleanup.conf
      notify: Restart docker

- name: Flush handlers so we can wait for docker to come up
  meta: flush_handlers

# Install each plugin using a looped include to make error handling in the included task simpler.
- name: Install docker plugin
  include_tasks: docker_plugin.yml
  loop: "{{ docker_plugins }}"
  loop_control:
    loop_var: docker_plugin

- name: Set docker systemd config
  import_tasks: systemd.yml

- name: Ensure docker service is started and enabled
  service:
    name: "{{ item }}"
    enabled: yes
    state: started
  with_items:
    - docker
