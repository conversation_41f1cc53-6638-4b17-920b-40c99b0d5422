---
- name: Remove legacy docker repo file
  file:
    path: "{{ yum_repo_dir }}/docker.repo"
    state: absent
  when:
    - ansible_os_family == 'RedHat'
    - not is_ostree

- name: Ensure old versions of Dock<PERSON> are not installed. | Debian
  apt:
    name: '{{ docker_remove_packages_apt }}'
    state: absent
  when:
    - ansible_os_family == 'Debian'
    - (docker_versioned_pkg[docker_version | string] is search('docker-ce'))


- name: Ensure podman not installed. | RedHat
  package:
    name: '{{ podman_remove_packages_yum }}'
    state: absent
  when:
    - ansible_os_family == 'RedHat'
    - (docker_versioned_pkg[docker_version | string] is search('docker-ce'))
    - not is_ostree


- name: Ensure old versions of Docker are not installed. | RedHat
  package:
    name: '{{ docker_remove_packages_yum }}'
    state: absent
  when:
    - ansible_os_family == 'RedHat'
    - (docker_versioned_pkg[docker_version | string] is search('docker-ce'))
    - not is_ostree
