[Unit]
Description=Docker Application Container Engine
Documentation=http://docs.docker.com
{% if ansible_os_family == "RedHat" %}
After=network.target {{ ' docker-storage-setup.service' if docker_container_storage_setup else '' }} containerd.service
BindsTo=containerd.service
{{ 'Wants=docker-storage-setup.service' if docker_container_storage_setup else '' }}
{% elif ansible_os_family == "Debian" %}
After=network.target docker.socket containerd.service
BindsTo=containerd.service
Wants=docker.socket
{% elif ansible_os_family == "Suse" %}
After=network.target lvm2-monitor.service SuSEfirewall2.service
# After=network.target containerd.service
# BindsTo=containerd.service
{% endif %}

[Service]
Type=notify
{% if docker_storage_options is defined %}
Environment="DOCKER_STORAGE_OPTIONS={{ docker_storage_options }}"
{% endif %}
Environment=GOTRACEBACK=crash
ExecReload=/bin/kill -s HUP $MAINPID
Delegate=yes
KillMode=process
ExecStart={{ docker_bin_dir }}/dockerd \
{% if ansible_os_family == "Suse" %}
          --add-runtime oci=/usr/sbin/docker-runc \
{% endif %}
          $DOCKER_OPTS \
          $DOCKER_STORAGE_OPTIONS \
          $DOCKER_DNS_OPTIONS
{% if not is_ostree and systemd_version.stdout|int >= 226 %}
TasksMax=infinity
{% endif %}
LimitNOFILE=1048576
LimitNPROC=1048576
LimitCORE=infinity
TimeoutStartSec=1min
# restart the docker process if it exits prematurely
Restart=on-failure
StartLimitBurst=3
StartLimitInterval=60s
# Set the cgroup slice of the service so that kube reserved takes effect
{% if kube_reserved is defined and kube_reserved|bool %}
Slice={{ kube_reserved_cgroups_for_service_slice }}
{% endif %}

[Install]
WantedBy=multi-user.target
