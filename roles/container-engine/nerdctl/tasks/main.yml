---
- name: Nerd<PERSON><PERSON> | Download nerdctl
  include_tasks: "../../../download/tasks/download_file.yml"
  vars:
    download: "{{ download_defaults | combine(downloads.nerdctl) }}"

- name: Nerdctl | Copy nerdctl binary from download dir
  copy:
    src: "{{ local_release_dir }}/nerdctl"
    dest: "{{ bin_dir }}/nerdctl"
    mode: 0755
    remote_src: true
    owner: root
    group: root
  become: true
  notify:
    - Get nerdctl completion
    - Install nerdctl completion

- name: Nerdctl | Create configuration dir
  file:
    path: /etc/nerdctl
    state: directory
    mode: 0755
    owner: root
    group: root
  become: true

- name: Nerdctl | Install nerdctl configuration
  template:
    src: nerdctl.toml.j2
    dest: /etc/nerdctl/nerdctl.toml
    mode: 0644
    owner: root
    group: root
  become: true
