---
- name: <PERSON><PERSON> | Download youki
  include_tasks: "../../../download/tasks/download_file.yml"
  vars:
    download: "{{ download_defaults | combine(downloads.youki) }}"

- name: You<PERSON> | Copy youki binary from download dir
  copy:
    src: "{{ local_release_dir }}/youki_{{ youki_version | regex_replace('\\.', '_') }}_linux/youki-{{ youki_version }}/youki"
    dest: "{{ youki_bin_dir }}/youki"
    mode: 0755
    remote_src: true
