---
- name: <PERSON><PERSON><PERSON><PERSON> | check if fedora coreos
  stat:
    path: /run/ostree-booted
    get_attributes: no
    get_checksum: no
    get_mime: no
  register: ostree

- name: Skopeo | set is_ostree
  set_fact:
    is_ostree: "{{ ostree.stat.exists }}"

- name: <PERSON>kopeo | Uninstall skopeo package managed by package manager
  package:
    name: skopeo
    state: absent
  when:
    - not (is_ostree or (ansible_distribution == "Flatcar Container Linux by Kinvolk") or (ansible_distribution == "Flatcar"))
  ignore_errors: true  # noqa ignore-errors

- name: Skopeo | Download skopeo binary
  include_tasks: "../../../download/tasks/download_file.yml"
  vars:
    download: "{{ download_defaults | combine(downloads.skopeo) }}"

- name: Copy skopeo binary from download dir
  copy:
    src: "{{ downloads.skopeo.dest }}"
    dest: "{{ bin_dir }}/skopeo"
    mode: 0755
    remote_src: true
