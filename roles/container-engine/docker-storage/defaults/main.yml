---
docker_container_storage_setup_repository: https://github.com/projectatomic/container-storage-setup.git
docker_container_storage_setup_version: v0.6.0
docker_container_storage_setup_profile_name: kubespray
docker_container_storage_setup_storage_driver: devicemapper
docker_container_storage_setup_container_thinpool: docker-pool
# It must be define a disk path for docker_container_storage_setup_devs.
# Otherwise docker-storage-setup will be executed incorrectly.
# docker_container_storage_setup_devs: /dev/vdb
docker_container_storage_setup_data_size: 40%FREE
docker_container_storage_setup_min_data_size: 2G
docker_container_storage_setup_chunk_size: 512K
docker_container_storage_setup_growpart: "false"
docker_container_storage_setup_auto_extend_pool: "yes"
docker_container_storage_setup_pool_autoextend_threshold: 60
docker_container_storage_setup_pool_autoextend_percent: 20
docker_container_storage_setup_device_wait_timeout: 60
docker_container_storage_setup_wipe_signatures: "false"
docker_container_storage_setup_container_root_lv_size: 40%FREE
