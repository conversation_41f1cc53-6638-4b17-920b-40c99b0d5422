---
# We keep these variables around to allow migration from package
# manager controlled installs to direct download ones.
containerd_package: 'containerd.io'
yum_repo_dir: /etc/yum.repos.d

# Keep minimal repo information around for cleanup
containerd_repo_info:
  repos:

# Ubuntu docker-ce repo
containerd_ubuntu_repo_base_url: "https://download.docker.com/linux/ubuntu"
containerd_ubuntu_repo_component: "stable"

# Debian docker-ce repo
containerd_debian_repo_base_url: "https://download.docker.com/linux/debian"
containerd_debian_repo_component: "stable"
