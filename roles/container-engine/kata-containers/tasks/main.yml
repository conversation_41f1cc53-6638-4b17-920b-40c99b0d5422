---
- name: Kata-containers | Download kata binary
  include_tasks: "../../../download/tasks/download_file.yml"
  vars:
    download: "{{ download_defaults | combine(downloads.kata_containers) }}"

- name: Kata-containers | Copy kata-containers binary
  unarchive:
    src: "{{ downloads.kata_containers.dest }}"
    dest: "/"
    mode: 0755
    owner: root
    group: root
    remote_src: yes

- name: Kata-containers | Create config directory
  file:
    path: "{{ kata_containers_config_dir }}"
    state: directory
    mode: 0755

- name: Kata-containers | Set configuration
  template:
    src: "{{ item }}.j2"
    dest: "{{ kata_containers_config_dir }}/{{ item }}"
    mode: 0644
  with_items:
    - configuration-qemu.toml

- name: Kata-containers | Set containerd bin
  vars:
    shim: "{{ item }}"
  template:
    dest: "{{ kata_containers_containerd_bin_dir }}/containerd-shim-kata-{{ item }}-v2"
    src: containerd-shim-kata-v2.j2
    mode: 0755
  with_items:
    - qemu

- name: Kata-containers | Load vhost kernel modules
  community.general.modprobe:
    state: present
    name: "{{ item }}"
  with_items:
    - vhost_vsock
    - vhost_net

- name: Kata-containers | Persist vhost kernel modules
  copy:
    dest: /etc/modules-load.d/kubespray-kata-containers.conf
    mode: 0644
    content: |
      vhost_vsock
      vhost_net
