---
- name: Wait for apiserver
  command: "{{ kubectl }} get nodes"
  environment:
    KUBECONFIG: "{{ ansible_env.HOME | default('/root') }}/.kube/config"
  register: apiserver_is_ready
  until: apiserver_is_ready.rc == 0
  retries: 6
  delay: 10
  changed_when: false
  when: groups['broken_kube_control_plane']

- name: Delete broken kube_control_plane nodes from cluster
  command: "{{ kubectl }} delete node {{ item }}"
  environment:
    KUBECONFIG: "{{ ansible_env.HOME | default('/root') }}/.kube/config"
  with_items: "{{ groups['broken_kube_control_plane'] }}"
  register: delete_broken_kube_masters
  failed_when: false
  when: groups['broken_kube_control_plane']

- name: Fail if unable to delete broken kube_control_plane nodes from cluster
  fail:
    msg: "Unable to delete broken kube_control_plane node: {{ item.item }}"
  loop: "{{ delete_broken_kube_masters.results }}"
  changed_when: false
  when:
    - groups['broken_kube_control_plane']
    - "item.rc != 0 and not 'NotFound' in item.stderr"
