---
local_release_dir: /tmp/releases
download_cache_dir: /tmp/kubespray_cache

# If this is true, debug information will be displayed but
# may contain some private data, so it is recommended to set it to false
# in the production environment.
unsafe_show_logs: false

# do not delete remote cache files after using them
# NOTE: Setting this parameter to TRUE is only really useful when developing kubespray
download_keep_remote_cache: false

# Only useful when download_run_once is false: Localy cached files and images are
# uploaded to kubernetes nodes. Also, images downloaded on those nodes are copied
# back to the ansible runner's cache, if they are not yet preset.
download_force_cache: false

# Used to only evaluate vars from download role
skip_downloads: false

# Optionally skip kubeadm images download
skip_kubeadm_images: false
kubeadm_images: {}

# if this is set to true will only download files once. Doesn't work
# on Flatcar Container Linux by Kinvolk unless the download_localhost is true and localhost
# is running another OS type. Default compress level is 1 (fastest).
download_run_once: false
download_compress: 1

# if this is set to true will download container
download_container: true

# if this is set to true, uses the localhost for download_run_once mode
# (requires docker and sudo to access docker). You may want this option for
# local caching of docker images or for Flatcar Container Linux by Kinvolk cluster nodes.
# Otherwise, uses the first node in the kube_control_plane group to store images
# in the download_run_once mode.
download_localhost: false

# Always pull images if set to True. Otherwise check by the repo's tag/digest.
download_always_pull: false

# Some problems may occur when downloading files over https proxy due to ansible bug
# https://github.com/ansible/ansible/issues/32750. Set this variable to False to disable
# SSL validation of get_url module. Note that kubespray will still be performing checksum validation.
download_validate_certs: true

# Use the first kube_control_plane if download_localhost is not set
download_delegate: "{% if download_localhost %}localhost{% else %}{{ groups['kube_control_plane'][0] }}{% endif %}"

# Allow control the times of download retries for files and containers
download_retries: 4

# The docker_image_info_command might seems weird but we are using raw/endraw and `{{ `{{` }}` to manage the double jinja2 processing
docker_image_pull_command: "{{ docker_bin_dir }}/docker pull"
docker_image_info_command: "{{ docker_bin_dir }}/docker images -q | xargs -i {{ '{{' }} docker_bin_dir }}/docker inspect -f {% raw %}'{{ '{{' }} if .RepoTags }}{{ '{{' }} join .RepoTags \",\" }}{{ '{{' }} end }}{{ '{{' }} if .RepoDigests }},{{ '{{' }} join .RepoDigests \",\" }}{{ '{{' }} end }}' {% endraw %} {} | tr '\n' ','"
nerdctl_image_info_command: "{{ bin_dir }}/nerdctl -n k8s.io images --format '{% raw %}{{ .Repository }}:{{ .Tag }}{% endraw %}' 2>/dev/null | grep -v ^:$ | tr '\n' ','"
# Using the ctr instead of nerdctl to workdaround the https://github.com/kubernetes-sigs/kubespray/issues/10670
nerdctl_image_pull_command: "{{ bin_dir }}/ctr -n k8s.io images pull{% if containerd_registries_mirrors is defined %} --hosts-dir {{ containerd_cfg_dir }}/certs.d{%- endif -%}"
crictl_image_info_command: "{{ bin_dir }}/crictl images --verbose | awk -F ': ' '/RepoTags|RepoDigests/ {print $2}' | tr '\n' ','"
crictl_image_pull_command: "{{ bin_dir }}/crictl pull"

image_command_tool: "{%- if container_manager == 'containerd' -%}nerdctl{%- elif container_manager == 'crio' -%}crictl{%- else -%}{{ container_manager }}{%- endif -%}"
image_command_tool_on_localhost: "{{ image_command_tool }}"

image_pull_command: "{{ lookup('vars', image_command_tool + '_image_pull_command') }}"
image_info_command: "{{ lookup('vars', image_command_tool + '_image_info_command') }}"
image_pull_command_on_localhost: "{{ lookup('vars', image_command_tool_on_localhost + '_image_pull_command') }}"
image_info_command_on_localhost: "{{ lookup('vars', image_command_tool_on_localhost + '_image_info_command') }}"

# Arch of Docker images and needed packages
image_arch: "{{ host_architecture | default('amd64') }}"

# Versions
kubeadm_version: "{{ kube_version }}"
crun_version: 1.14.4
runc_version: v1.1.12
kata_containers_version: 3.1.3
youki_version: 0.1.0
gvisor_version: 20240305
containerd_version: 1.7.16
cri_dockerd_version: 0.3.11

# this is relevant when container_manager == 'docker'
docker_containerd_version: 1.6.28

# gcr and kubernetes image repo define
gcr_image_repo: "gcr.io"
kube_image_repo: "registry.k8s.io"

# docker image repo define
docker_image_repo: "docker.io"

# quay image repo define
quay_image_repo: "quay.io"

# github image repo define (ex multus only use that)
github_image_repo: "ghcr.io"

# TODO(mattymo): Move calico versions to roles/network_plugins/calico/defaults
# after migration to container download
calico_version: "v3.27.3"
calico_ctl_version: "{{ calico_version }}"
calico_cni_version: "{{ calico_version }}"
calico_flexvol_version: "{{ calico_version }}"
calico_policy_version: "{{ calico_version }}"
calico_typha_version: "{{ calico_version }}"
calico_apiserver_version: "{{ calico_version }}"
typha_enabled: false
calico_apiserver_enabled: false

flannel_version: "v0.22.0"
flannel_cni_version: "v1.1.2"
cni_version: "v1.3.0"
weave_version: 2.8.1

cilium_version: "v1.15.4"
cilium_cli_version: "v0.16.0"
cilium_enable_hubble: false

kube_ovn_version: "v1.11.5"
kube_ovn_dpdk_version: "19.11-{{ kube_ovn_version }}"
kube_router_version: "v2.0.0"
multus_version: "v3.8"
helm_version: "v3.14.2"
nerdctl_version: "1.7.4"
krew_version: "v0.4.4"
skopeo_version: "v1.15.0"

# Get kubernetes major version (i.e. 1.17.4 => 1.17)
kube_major_version: "{{ kube_version | regex_replace('^v([0-9])+\\.([0-9]+)\\.[0-9]+', 'v\\1.\\2') }}"

pod_infra_supported_versions:
  v1.29: "3.9"
  v1.28: "3.9"
  v1.27: "3.9"
pod_infra_version: "{{ pod_infra_supported_versions[kube_major_version] }}"

etcd_supported_versions:
  v1.29: "v3.5.12"
  v1.28: "v3.5.12"
  v1.27: "v3.5.12"
etcd_version: "{{ etcd_supported_versions[kube_major_version] }}"

crictl_supported_versions:
  v1.29: "v1.29.0"
  v1.28: "v1.28.0"
  v1.27: "v1.27.1"
crictl_version: "{{ crictl_supported_versions[kube_major_version] }}"

crio_supported_versions:
  v1.29: v1.29.1
  v1.28: v1.28.4
  v1.27: v1.27.4
crio_version: "{{ crio_supported_versions[kube_major_version] }}"

# Scheduler plugins doesn't build for K8s 1.29 yet
scheduler_plugins_supported_versions:
  v1.29: 0
  v1.28: v0.28.9
  v1.27: v0.27.8
scheduler_plugins_version: "{{ scheduler_plugins_supported_versions[kube_major_version] }}"

yq_version: "v4.42.1"

github_url: https://github.com
dl_k8s_io_url: https://dl.k8s.io
storage_googleapis_url: https://storage.googleapis.com
get_helm_url: https://get.helm.sh

# Download URLs
kubelet_download_url: "{{ dl_k8s_io_url }}/release/{{ kube_version }}/bin/linux/{{ image_arch }}/kubelet"
kubectl_download_url: "{{ dl_k8s_io_url }}/release/{{ kube_version }}/bin/linux/{{ image_arch }}/kubectl"
kubeadm_download_url: "{{ dl_k8s_io_url }}/release/{{ kubeadm_version }}/bin/linux/{{ image_arch }}/kubeadm"
etcd_download_url: "{{ github_url }}/etcd-io/etcd/releases/download/{{ etcd_version }}/etcd-{{ etcd_version }}-linux-{{ image_arch }}.tar.gz"
cni_download_url: "{{ github_url }}/containernetworking/plugins/releases/download/{{ cni_version }}/cni-plugins-linux-{{ image_arch }}-{{ cni_version }}.tgz"
calicoctl_download_url: "{{ github_url }}/projectcalico/calico/releases/download/{{ calico_ctl_version }}/calicoctl-linux-{{ image_arch }}"
calico_crds_download_url: "{{ github_url }}/projectcalico/calico/archive/{{ calico_version }}.tar.gz"
ciliumcli_download_url: "{{ github_url }}/cilium/cilium-cli/releases/download/{{ cilium_cli_version }}/cilium-linux-{{ image_arch }}.tar.gz"
crictl_download_url: "{{ github_url }}/kubernetes-sigs/cri-tools/releases/download/{{ crictl_version }}/crictl-{{ crictl_version }}-{{ ansible_system | lower }}-{{ image_arch }}.tar.gz"
crio_download_url: "{{ storage_googleapis_url }}/cri-o/artifacts/cri-o.{{ image_arch }}.{{ crio_version }}.tar.gz"
helm_download_url: "{{ get_helm_url }}/helm-{{ helm_version }}-linux-{{ image_arch }}.tar.gz"
runc_download_url: "{{ github_url }}/opencontainers/runc/releases/download/{{ runc_version }}/runc.{{ image_arch }}"
crun_download_url: "{{ github_url }}/containers/crun/releases/download/{{ crun_version }}/crun-{{ crun_version }}-linux-{{ image_arch }}"
youki_download_url: "{{ github_url }}/containers/youki/releases/download/v{{ youki_version }}/youki_{{ youki_version | regex_replace('\\.', '_') }}_linux.tar.gz"
kata_containers_download_url: "{{ github_url }}/kata-containers/kata-containers/releases/download/{{ kata_containers_version }}/kata-static-{{ kata_containers_version }}-{{ ansible_architecture }}.tar.xz"
# gVisor only supports amd64 and uses x86_64 to in the download link
gvisor_runsc_download_url: "{{ storage_googleapis_url }}/gvisor/releases/release/{{ gvisor_version }}/{{ ansible_architecture }}/runsc"
gvisor_containerd_shim_runsc_download_url: "{{ storage_googleapis_url }}/gvisor/releases/release/{{ gvisor_version }}/{{ ansible_architecture }}/containerd-shim-runsc-v1"
nerdctl_download_url: "{{ github_url }}/containerd/nerdctl/releases/download/v{{ nerdctl_version }}/nerdctl-{{ nerdctl_version }}-{{ ansible_system | lower }}-{{ image_arch }}.tar.gz"
krew_download_url: "{{ github_url }}/kubernetes-sigs/krew/releases/download/{{ krew_version }}/krew-{{ host_os }}_{{ image_arch }}.tar.gz"
containerd_download_url: "{{ github_url }}/containerd/containerd/releases/download/v{{ containerd_version }}/containerd-{{ containerd_version }}-linux-{{ image_arch }}.tar.gz"
cri_dockerd_download_url: "{{ github_url }}/Mirantis/cri-dockerd/releases/download/v{{ cri_dockerd_version }}/cri-dockerd-{{ cri_dockerd_version }}.{{ image_arch }}.tgz"
skopeo_download_url: "{{ github_url }}/lework/skopeo-binary/releases/download/{{ skopeo_version }}/skopeo-linux-{{ image_arch }}"
yq_download_url: "{{ github_url }}/mikefarah/yq/releases/download/{{ yq_version }}/yq_linux_{{ image_arch }}"

etcd_binary_checksum: "{{ etcd_binary_checksums[image_arch][etcd_version] }}"
cni_binary_checksum: "{{ cni_binary_checksums[image_arch][cni_version] }}"
kubelet_binary_checksum: "{{ kubelet_checksums[image_arch][kube_version] }}"
kubectl_binary_checksum: "{{ kubectl_checksums[image_arch][kube_version] }}"
kubeadm_binary_checksum: "{{ kubeadm_checksums[image_arch][kubeadm_version] }}"
yq_binary_checksum: "{{ yq_checksums[image_arch][yq_version] }}"
calicoctl_binary_checksum: "{{ calicoctl_binary_checksums[image_arch][calico_ctl_version] }}"
calico_crds_archive_checksum: "{{ calico_crds_archive_checksums[calico_version] }}"
ciliumcli_binary_checksum: "{{ ciliumcli_binary_checksums[image_arch][cilium_cli_version] }}"
crictl_binary_checksum: "{{ crictl_checksums[image_arch][crictl_version] }}"
crio_archive_checksum: "{{ crio_archive_checksums[image_arch][crio_version] }}"
cri_dockerd_archive_checksum: "{{ cri_dockerd_archive_checksums[image_arch][cri_dockerd_version] }}"
helm_archive_checksum: "{{ helm_archive_checksums[image_arch][helm_version] }}"
runc_binary_checksum: "{{ runc_checksums[image_arch][runc_version] }}"
crun_binary_checksum: "{{ crun_checksums[image_arch][crun_version] }}"
youki_archive_checksum: "{{ youki_checksums[image_arch][youki_version] }}"
kata_containers_binary_checksum: "{{ kata_containers_binary_checksums[image_arch][kata_containers_version] }}"
gvisor_runsc_binary_checksum: "{{ gvisor_runsc_binary_checksums[image_arch][gvisor_version] }}"
gvisor_containerd_shim_binary_checksum: "{{ gvisor_containerd_shim_binary_checksums[image_arch][gvisor_version] }}"
nerdctl_archive_checksum: "{{ nerdctl_archive_checksums[image_arch][nerdctl_version] }}"
krew_archive_checksum: "{{ krew_archive_checksums[host_os][image_arch][krew_version] }}"
containerd_archive_checksum: "{{ containerd_archive_checksums[image_arch][containerd_version] }}"
skopeo_binary_checksum: "{{ skopeo_binary_checksums[image_arch][skopeo_version] }}"

# Containers
# In some cases, we need a way to set --registry-mirror or --insecure-registry for docker,
# it helps a lot for local private development or bare metal environment.
# So you need define --registry-mirror or --insecure-registry, and modify the following url address.
# example:
# You need to deploy kubernetes cluster on local private development.
# Also provide the address of your own private registry.
# And use --insecure-registry options for docker
kube_proxy_image_repo: "{{ kube_image_repo }}/kube-proxy"
etcd_image_repo: "{{ quay_image_repo }}/coreos/etcd"
etcd_image_tag: "{{ etcd_version }}"
flannel_image_repo: "{{ docker_image_repo }}/flannel/flannel"
flannel_image_tag: "{{ flannel_version }}"
flannel_init_image_repo: "{{ docker_image_repo }}/flannel/flannel-cni-plugin"
flannel_init_image_tag: "{{ flannel_cni_version }}"
calico_node_image_repo: "{{ quay_image_repo }}/calico/node"
calico_node_image_tag: "{{ calico_version }}"
calico_cni_image_repo: "{{ quay_image_repo }}/calico/cni"
calico_cni_image_tag: "{{ calico_cni_version }}"
calico_flexvol_image_repo: "{{ quay_image_repo }}/calico/pod2daemon-flexvol"
calico_flexvol_image_tag: "{{ calico_flexvol_version }}"
calico_policy_image_repo: "{{ quay_image_repo }}/calico/kube-controllers"
calico_policy_image_tag: "{{ calico_policy_version }}"
calico_typha_image_repo: "{{ quay_image_repo }}/calico/typha"
calico_typha_image_tag: "{{ calico_typha_version }}"
calico_apiserver_image_repo: "{{ quay_image_repo }}/calico/apiserver"
calico_apiserver_image_tag: "{{ calico_apiserver_version }}"
pod_infra_image_repo: "{{ kube_image_repo }}/pause"
pod_infra_image_tag: "{{ pod_infra_version }}"
netcheck_version: "v1.2.2"
netcheck_agent_image_repo: "{{ docker_image_repo }}/mirantis/k8s-netchecker-agent"
netcheck_agent_image_tag: "{{ netcheck_version }}"
netcheck_server_image_repo: "{{ docker_image_repo }}/mirantis/k8s-netchecker-server"
netcheck_server_image_tag: "{{ netcheck_version }}"
netcheck_etcd_image_tag: "v3.4.17"
weave_kube_image_repo: "{{ docker_image_repo }}/weaveworks/weave-kube"
weave_kube_image_tag: "{{ weave_version }}"
weave_npc_image_repo: "{{ docker_image_repo }}/weaveworks/weave-npc"
weave_npc_image_tag: "{{ weave_version }}"
cilium_image_repo: "{{ quay_image_repo }}/cilium/cilium"
cilium_image_tag: "{{ cilium_version }}"
cilium_operator_image_repo: "{{ quay_image_repo }}/cilium/operator"
cilium_operator_image_tag: "{{ cilium_version }}"
cilium_hubble_relay_image_repo: "{{ quay_image_repo }}/cilium/hubble-relay"
cilium_hubble_relay_image_tag: "{{ cilium_version }}"
cilium_hubble_certgen_image_repo: "{{ quay_image_repo }}/cilium/certgen"
cilium_hubble_certgen_image_tag: "v0.1.8"
cilium_hubble_ui_image_repo: "{{ quay_image_repo }}/cilium/hubble-ui"
cilium_hubble_ui_image_tag: "v0.11.0"
cilium_hubble_ui_backend_image_repo: "{{ quay_image_repo }}/cilium/hubble-ui-backend"
cilium_hubble_ui_backend_image_tag: "v0.11.0"
cilium_hubble_envoy_image_repo: "{{ docker_image_repo }}/envoyproxy/envoy"
cilium_hubble_envoy_image_tag: "v1.22.5"
kube_ovn_container_image_repo: "{{ docker_image_repo }}/kubeovn/kube-ovn"
kube_ovn_container_image_tag: "{{ kube_ovn_version }}"
kube_ovn_dpdk_container_image_repo: "{{ docker_image_repo }}/kubeovn/kube-ovn-dpdk"
kube_ovn_dpdk_container_image_tag: "{{ kube_ovn_dpdk_version }}"
kube_router_image_repo: "{{ docker_image_repo }}/cloudnativelabs/kube-router"
kube_router_image_tag: "{{ kube_router_version }}"
multus_image_repo: "{{ github_image_repo }}/k8snetworkplumbingwg/multus-cni"
multus_image_tag: "{{ multus_version }}"
external_openstack_cloud_controller_image_repo: "registry.k8s.io/provider-os/openstack-cloud-controller-manager"
external_openstack_cloud_controller_image_tag: "v1.28.2"

kube_vip_image_repo: "{{ github_image_repo }}/kube-vip/kube-vip"
kube_vip_image_tag: v0.8.0
nginx_image_repo: "{{ docker_image_repo }}/library/nginx"
nginx_image_tag: 1.25.2-alpine
haproxy_image_repo: "{{ docker_image_repo }}/library/haproxy"
haproxy_image_tag: 2.8.2-alpine

# Coredns version should be supported by corefile-migration (or at least work with)
# bundle with kubeadm; if not 'basic' upgrade can sometimes fail

coredns_version: "{{ 'v1.11.1' if (kube_version is version('v1.29.0', '>=')) else 'v1.10.1' }}"
coredns_image_is_namespaced: "{{ (coredns_version is version('v1.7.1', '>=')) }}"

coredns_image_repo: "{{ kube_image_repo }}{{ '/coredns/coredns' if (coredns_image_is_namespaced | bool) else '/coredns' }}"
coredns_image_tag: "{{ coredns_version if (coredns_image_is_namespaced | bool) else (coredns_version | regex_replace('^v', '')) }}"

nodelocaldns_version: "1.22.28"
nodelocaldns_image_repo: "{{ kube_image_repo }}/dns/k8s-dns-node-cache"
nodelocaldns_image_tag: "{{ nodelocaldns_version }}"

dnsautoscaler_version: v1.8.8
dnsautoscaler_image_repo: "{{ kube_image_repo }}/cpa/cluster-proportional-autoscaler"
dnsautoscaler_image_tag: "{{ dnsautoscaler_version }}"

scheduler_plugins_controller_image_repo: "{{ kube_image_repo }}/scheduler-plugins/controller"
scheduler_plugins_controller_image_tag: "{{ scheduler_plugins_version }}"
scheduler_plugins_scheduler_image_repo: "{{ kube_image_repo }}/scheduler-plugins/kube-scheduler"
scheduler_plugins_scheduler_image_tag: "{{ scheduler_plugins_version }}"

registry_version: "2.8.1"
registry_image_repo: "{{ docker_image_repo }}/library/registry"
registry_image_tag: "{{ registry_version }}"
metrics_server_version: "v0.7.0"
metrics_server_image_repo: "{{ kube_image_repo }}/metrics-server/metrics-server"
metrics_server_image_tag: "{{ metrics_server_version }}"
local_volume_provisioner_version: "v2.5.0"
local_volume_provisioner_image_repo: "{{ kube_image_repo }}/sig-storage/local-volume-provisioner"
local_volume_provisioner_image_tag: "{{ local_volume_provisioner_version }}"
cephfs_provisioner_version: "v2.1.0-k8s1.11"
cephfs_provisioner_image_repo: "{{ quay_image_repo }}/external_storage/cephfs-provisioner"
cephfs_provisioner_image_tag: "{{ cephfs_provisioner_version }}"
rbd_provisioner_version: "v2.1.1-k8s1.11"
rbd_provisioner_image_repo: "{{ quay_image_repo }}/external_storage/rbd-provisioner"
rbd_provisioner_image_tag: "{{ rbd_provisioner_version }}"
local_path_provisioner_version: "v0.0.24"
local_path_provisioner_image_repo: "{{ docker_image_repo }}/rancher/local-path-provisioner"
local_path_provisioner_image_tag: "{{ local_path_provisioner_version }}"
ingress_nginx_version: "v1.10.1"
ingress_nginx_controller_image_repo: "{{ kube_image_repo }}/ingress-nginx/controller"
ingress_nginx_opentelemetry_image_repo: "{{ kube_image_repo }}/ingress-nginx/opentelemetry"
ingress_nginx_controller_image_tag: "{{ ingress_nginx_version }}"
ingress_nginx_opentelemetry_image_tag: "v20230721-3e2062ee5"
ingress_nginx_kube_webhook_certgen_image_repo: "{{ kube_image_repo }}/ingress-nginx/kube-webhook-certgen"
ingress_nginx_kube_webhook_certgen_image_tag: "v1.4.1"
alb_ingress_image_repo: "{{ docker_image_repo }}/amazon/aws-alb-ingress-controller"
alb_ingress_image_tag: "v1.1.9"
cert_manager_version: "v1.13.2"
cert_manager_controller_image_repo: "{{ quay_image_repo }}/jetstack/cert-manager-controller"
cert_manager_controller_image_tag: "{{ cert_manager_version }}"
cert_manager_cainjector_image_repo: "{{ quay_image_repo }}/jetstack/cert-manager-cainjector"
cert_manager_cainjector_image_tag: "{{ cert_manager_version }}"
cert_manager_webhook_image_repo: "{{ quay_image_repo }}/jetstack/cert-manager-webhook"
cert_manager_webhook_image_tag: "{{ cert_manager_version }}"

csi_attacher_image_repo: "{{ kube_image_repo }}/sig-storage/csi-attacher"
csi_attacher_image_tag: "v3.3.0"
csi_provisioner_image_repo: "{{ kube_image_repo }}/sig-storage/csi-provisioner"
csi_provisioner_image_tag: "v3.0.0"
csi_snapshotter_image_repo: "{{ kube_image_repo }}/sig-storage/csi-snapshotter"
csi_snapshotter_image_tag: "v5.0.0"
csi_resizer_image_repo: "{{ kube_image_repo }}/sig-storage/csi-resizer"
csi_resizer_image_tag: "v1.3.0"
csi_node_driver_registrar_image_repo: "{{ kube_image_repo }}/sig-storage/csi-node-driver-registrar"
csi_node_driver_registrar_image_tag: "v2.4.0"
csi_livenessprobe_image_repo: "{{ kube_image_repo }}/sig-storage/livenessprobe"
csi_livenessprobe_image_tag: "v2.5.0"

snapshot_controller_supported_versions:
  v1.29: "v7.0.2"
  v1.28: "v7.0.2"
  v1.27: "v7.0.2"
snapshot_controller_image_repo: "{{ kube_image_repo }}/sig-storage/snapshot-controller"
snapshot_controller_image_tag: "{{ snapshot_controller_supported_versions[kube_major_version] }}"

cinder_csi_plugin_version: "v1.29.0"
cinder_csi_plugin_image_repo: "{{ kube_image_repo }}/provider-os/cinder-csi-plugin"
cinder_csi_plugin_image_tag: "{{ cinder_csi_plugin_version }}"

aws_ebs_csi_plugin_version: "v0.5.0"
aws_ebs_csi_plugin_image_repo: "{{ docker_image_repo }}/amazon/aws-ebs-csi-driver"
aws_ebs_csi_plugin_image_tag: "{{ aws_ebs_csi_plugin_version }}"

gcp_pd_csi_plugin_version: "v1.9.2"
gcp_pd_csi_plugin_image_repo: "{{ kube_image_repo }}/cloud-provider-gcp/gcp-compute-persistent-disk-csi-driver"
gcp_pd_csi_plugin_image_tag: "{{ gcp_pd_csi_plugin_version }}"

azure_csi_image_repo: "mcr.microsoft.com/oss/kubernetes-csi"
azure_csi_provisioner_image_tag: "v2.2.2"
azure_csi_attacher_image_tag: "v3.3.0"
azure_csi_resizer_image_tag: "v1.3.0"
azure_csi_livenessprobe_image_tag: "v2.5.0"
azure_csi_node_registrar_image_tag: "v2.4.0"
azure_csi_snapshotter_image_tag: "v3.0.3"
azure_csi_plugin_version: "v1.10.0"
azure_csi_plugin_image_repo: "mcr.microsoft.com/k8s/csi"
azure_csi_plugin_image_tag: "{{ azure_csi_plugin_version }}"

gcp_pd_csi_image_repo: "gke.gcr.io"
gcp_pd_csi_driver_image_tag: "v0.7.0-gke.0"
gcp_pd_csi_provisioner_image_tag: "v1.5.0-gke.0"
gcp_pd_csi_attacher_image_tag: "v2.1.1-gke.0"
gcp_pd_csi_resizer_image_tag: "v0.4.0-gke.0"
gcp_pd_csi_registrar_image_tag: "v1.2.0-gke.0"

dashboard_image_repo: "{{ docker_image_repo }}/kubernetesui/dashboard"
dashboard_image_tag: "v2.7.0"
dashboard_metrics_scraper_repo: "{{ docker_image_repo }}/kubernetesui/metrics-scraper"
dashboard_metrics_scraper_tag: "v1.0.8"

metallb_speaker_image_repo: "{{ quay_image_repo }}/metallb/speaker"
metallb_controller_image_repo: "{{ quay_image_repo }}/metallb/controller"
metallb_version: v0.13.9

node_feature_discovery_version: v0.14.2
node_feature_discovery_image_repo: "{{ kube_image_repo }}/nfd/node-feature-discovery"
node_feature_discovery_image_tag: "{{ node_feature_discovery_version }}"

downloads:
  netcheck_server:
    enabled: "{{ deploy_netchecker }}"
    container: true
    repo: "{{ netcheck_server_image_repo }}"
    tag: "{{ netcheck_server_image_tag }}"
    sha256: "{{ netcheck_server_digest_checksum | default(None) }}"
    groups:
      - k8s_cluster

  netcheck_agent:
    enabled: "{{ deploy_netchecker }}"
    container: true
    repo: "{{ netcheck_agent_image_repo }}"
    tag: "{{ netcheck_agent_image_tag }}"
    sha256: "{{ netcheck_agent_digest_checksum | default(None) }}"
    groups:
      - k8s_cluster

  etcd:
    container: "{{ etcd_deployment_type != 'host' }}"
    file: "{{ etcd_deployment_type == 'host' }}"
    enabled: true
    version: "{{ etcd_version }}"
    dest: "{{ local_release_dir }}/etcd-{{ etcd_version }}-linux-{{ image_arch }}.tar.gz"
    repo: "{{ etcd_image_repo }}"
    tag: "{{ etcd_image_tag }}"
    sha256: >-
      {{ etcd_binary_checksum if (etcd_deployment_type == 'host')
      else etcd_digest_checksum | d(None) }}
    url: "{{ etcd_download_url }}"
    unarchive: "{{ etcd_deployment_type == 'host' }}"
    owner: "root"
    mode: "0755"
    groups:
      - etcd

  cni:
    enabled: true
    file: true
    version: "{{ cni_version }}"
    dest: "{{ local_release_dir }}/cni-plugins-linux-{{ image_arch }}-{{ cni_version }}.tgz"
    sha256: "{{ cni_binary_checksum }}"
    url: "{{ cni_download_url }}"
    unarchive: false
    owner: "root"
    mode: "0755"
    groups:
      - k8s_cluster

  kubeadm:
    enabled: true
    file: true
    version: "{{ kubeadm_version }}"
    dest: "{{ local_release_dir }}/kubeadm-{{ kubeadm_version }}-{{ image_arch }}"
    sha256: "{{ kubeadm_binary_checksum }}"
    url: "{{ kubeadm_download_url }}"
    unarchive: false
    owner: "root"
    mode: "0755"
    groups:
      - k8s_cluster

  kubelet:
    enabled: true
    file: true
    version: "{{ kube_version }}"
    dest: "{{ local_release_dir }}/kubelet-{{ kube_version }}-{{ image_arch }}"
    sha256: "{{ kubelet_binary_checksum }}"
    url: "{{ kubelet_download_url }}"
    unarchive: false
    owner: "root"
    mode: "0755"
    groups:
      - k8s_cluster

  kubectl:
    enabled: true
    file: true
    version: "{{ kube_version }}"
    dest: "{{ local_release_dir }}/kubectl-{{ kube_version }}-{{ image_arch }}"
    sha256: "{{ kubectl_binary_checksum }}"
    url: "{{ kubectl_download_url }}"
    unarchive: false
    owner: "root"
    mode: "0755"
    groups:
      - kube_control_plane

  crictl:
    file: true
    enabled: true
    version: "{{ crictl_version }}"
    dest: "{{ local_release_dir }}/crictl-{{ crictl_version }}-linux-{{ image_arch }}.tar.gz"
    sha256: "{{ crictl_binary_checksum }}"
    url: "{{ crictl_download_url }}"
    unarchive: true
    owner: "root"
    mode: "0755"
    groups:
      - k8s_cluster

  crio:
    file: true
    enabled: "{{ container_manager == 'crio' }}"
    version: "{{ crio_version }}"
    dest: "{{ local_release_dir }}/cri-o.{{ image_arch }}.{{ crio_version }}tar.gz"
    sha256: "{{ crio_archive_checksum }}"
    url: "{{ crio_download_url }}"
    unarchive: true
    owner: "root"
    mode: "0755"
    groups:
      - k8s_cluster

  cri_dockerd:
    file: true
    enabled: "{{ container_manager == 'docker' }}"
    version: "{{ cri_dockerd_version }}"
    dest: "{{ local_release_dir }}/cri-dockerd-{{ cri_dockerd_version }}.{{ image_arch }}.tar.gz"
    sha256: "{{ cri_dockerd_archive_checksum }}"
    url: "{{ cri_dockerd_download_url }}"
    unarchive: true
    unarchive_extra_opts:
      - --strip=1
    owner: "root"
    mode: "0755"
    groups:
      - k8s_cluster

  crun:
    file: true
    enabled: "{{ crun_enabled }}"
    version: "{{ crun_version }}"
    dest: "{{ local_release_dir }}/crun-{{ crun_version }}-{{ image_arch }}"
    sha256: "{{ crun_binary_checksum }}"
    url: "{{ crun_download_url }}"
    unarchive: false
    owner: "root"
    mode: "0755"
    groups:
      - k8s_cluster

  youki:
    file: true
    enabled: "{{ youki_enabled }}"
    version: "{{ youki_version }}"
    dest: "{{ local_release_dir }}/youki_{{ youki_version | regex_replace('\\.', '_') }}_linux.tar.gz"
    sha256: "{{ youki_archive_checksum }}"
    url: "{{ youki_download_url }}"
    unarchive: true
    owner: "root"
    mode: "0755"
    groups:
      - k8s_cluster

  runc:
    file: true
    enabled: "{{ container_manager == 'containerd' }}"
    version: "{{ runc_version }}"
    dest: "{{ local_release_dir }}/runc-{{ runc_version }}.{{ image_arch }}"
    sha256: "{{ runc_binary_checksum }}"
    url: "{{ runc_download_url }}"
    unarchive: false
    owner: "root"
    mode: "0755"
    groups:
      - k8s_cluster

  kata_containers:
    enabled: "{{ kata_containers_enabled }}"
    file: true
    version: "{{ kata_containers_version }}"
    dest: "{{ local_release_dir }}/kata-static-{{ kata_containers_version }}-{{ image_arch }}.tar.xz"
    sha256: "{{ kata_containers_binary_checksum }}"
    url: "{{ kata_containers_download_url }}"
    unarchive: false
    owner: "root"
    mode: "0755"
    groups:
      - k8s_cluster

  containerd:
    enabled: "{{ container_manager == 'containerd' }}"
    file: true
    version: "{{ containerd_version }}"
    dest: "{{ local_release_dir }}/containerd-{{ containerd_version }}-linux-{{ image_arch }}.tar.gz"
    sha256: "{{ containerd_archive_checksum }}"
    url: "{{ containerd_download_url }}"
    unarchive: false
    owner: "root"
    mode: "0755"
    groups:
      - k8s_cluster

  gvisor_runsc:
    enabled: "{{ gvisor_enabled }}"
    file: true
    version: "{{ gvisor_version }}"
    dest: "{{ local_release_dir }}/gvisor-runsc-{{ gvisor_version }}-{{ ansible_architecture }}"
    sha256: "{{ gvisor_runsc_binary_checksum }}"
    url: "{{ gvisor_runsc_download_url }}"
    unarchive: false
    owner: "root"
    mode: 755
    groups:
      - k8s_cluster

  gvisor_containerd_shim:
    enabled: "{{ gvisor_enabled }}"
    file: true
    version: "{{ gvisor_version }}"
    dest: "{{ local_release_dir }}/gvisor-containerd-shim-runsc-v1-{{ gvisor_version }}-{{ ansible_architecture }}"
    sha256: "{{ gvisor_containerd_shim_binary_checksum }}"
    url: "{{ gvisor_containerd_shim_runsc_download_url }}"
    unarchive: false
    owner: "root"
    mode: 755
    groups:
      - k8s_cluster

  nerdctl:
    file: true
    enabled: "{{ container_manager == 'containerd' }}"
    version: "{{ nerdctl_version }}"
    dest: "{{ local_release_dir }}/nerdctl-{{ nerdctl_version }}-linux-{{ image_arch }}.tar.gz"
    sha256: "{{ nerdctl_archive_checksum }}"
    url: "{{ nerdctl_download_url }}"
    unarchive: true
    owner: "root"
    mode: "0755"
    groups:
      - k8s_cluster

  skopeo:
    file: true
    enabled: "{{ container_manager == 'crio' }}"
    version: "{{ skopeo_version }}"
    dest: "{{ local_release_dir }}/skopeo-{{ skopeo_version }}-{{ image_arch }}"
    sha256: "{{ skopeo_binary_checksum }}"
    url: "{{ skopeo_download_url }}"
    unarchive: false
    owner: "root"
    mode: "0755"
    groups:
      - kube_control_plane

  cilium:
    enabled: "{{ kube_network_plugin == 'cilium' or cilium_deploy_additionally | default(false) | bool }}"
    container: true
    repo: "{{ cilium_image_repo }}"
    tag: "{{ cilium_image_tag }}"
    sha256: "{{ cilium_digest_checksum | default(None) }}"
    groups:
      - k8s_cluster

  cilium_operator:
    enabled: "{{ kube_network_plugin == 'cilium' or cilium_deploy_additionally | default(false) | bool }}"
    container: true
    repo: "{{ cilium_operator_image_repo }}"
    tag: "{{ cilium_operator_image_tag }}"
    sha256: "{{ cilium_operator_digest_checksum | default(None) }}"
    groups:
      - k8s_cluster

  cilium_hubble_relay:
    enabled: "{{ cilium_enable_hubble }}"
    container: true
    repo: "{{ cilium_hubble_relay_image_repo }}"
    tag: "{{ cilium_hubble_relay_image_tag }}"
    sha256: "{{ cilium_hubble_relay_digest_checksum | default(None) }}"
    groups:
      - k8s_cluster

  cilium_hubble_certgen:
    enabled: "{{ cilium_enable_hubble }}"
    container: true
    repo: "{{ cilium_hubble_certgen_image_repo }}"
    tag: "{{ cilium_hubble_certgen_image_tag }}"
    sha256: "{{ cilium_hubble_certgen_digest_checksum | default(None) }}"
    groups:
      - k8s_cluster

  cilium_hubble_ui:
    enabled: "{{ cilium_enable_hubble }}"
    container: true
    repo: "{{ cilium_hubble_ui_image_repo }}"
    tag: "{{ cilium_hubble_ui_image_tag }}"
    sha256: "{{ cilium_hubble_ui_digest_checksum | default(None) }}"
    groups:
      - k8s_cluster

  cilium_hubble_ui_backend:
    enabled: "{{ cilium_enable_hubble }}"
    container: true
    repo: "{{ cilium_hubble_ui_backend_image_repo }}"
    tag: "{{ cilium_hubble_ui_backend_image_tag }}"
    sha256: "{{ cilium_hubble_ui_backend_digest_checksum | default(None) }}"
    groups:
      - k8s_cluster

  cilium_hubble_envoy:
    enabled: "{{ cilium_enable_hubble }}"
    container: true
    repo: "{{ cilium_hubble_envoy_image_repo }}"
    tag: "{{ cilium_hubble_envoy_image_tag }}"
    sha256: "{{ cilium_hubble_envoy_digest_checksum | default(None) }}"
    groups:
      - k8s_cluster

  ciliumcli:
    enabled: "{{ kube_network_plugin == 'cilium' or cilium_deploy_additionally | default(false) | bool }}"
    file: true
    version: "{{ cilium_cli_version }}"
    dest: "{{ local_release_dir }}/cilium-{{ cilium_cli_version }}-{{ image_arch }}.tar.gz"
    sha256: "{{ ciliumcli_binary_checksum }}"
    url: "{{ ciliumcli_download_url }}"
    unarchive: true
    owner: "root"
    mode: "0755"
    groups:
      - k8s_cluster

  multus:
    enabled: "{{ kube_network_plugin_multus }}"
    container: true
    repo: "{{ multus_image_repo }}"
    tag: "{{ multus_image_tag }}"
    sha256: "{{ multus_digest_checksum | default(None) }}"
    groups:
      - k8s_cluster

  flannel:
    enabled: "{{ kube_network_plugin == 'flannel' }}"
    container: true
    repo: "{{ flannel_image_repo }}"
    tag: "{{ flannel_image_tag }}"
    sha256: "{{ flannel_digest_checksum | default(None) }}"
    groups:
      - k8s_cluster

  flannel_init:
    enabled: "{{ kube_network_plugin == 'flannel' }}"
    container: true
    repo: "{{ flannel_init_image_repo }}"
    tag: "{{ flannel_init_image_tag }}"
    sha256: "{{ flannel_init_digest_checksum | default(None) }}"
    groups:
      - k8s_cluster

  calicoctl:
    enabled: "{{ kube_network_plugin == 'calico' }}"
    file: true
    version: "{{ calico_ctl_version }}"
    dest: "{{ local_release_dir }}/calicoctl-{{ calico_ctl_version }}-{{ image_arch }}"
    sha256: "{{ calicoctl_binary_checksum }}"
    url: "{{ calicoctl_download_url }}"
    unarchive: false
    owner: "root"
    mode: "0755"
    groups:
      - k8s_cluster

  calico_node:
    enabled: "{{ kube_network_plugin == 'calico' }}"
    container: true
    repo: "{{ calico_node_image_repo }}"
    tag: "{{ calico_node_image_tag }}"
    sha256: "{{ calico_node_digest_checksum | default(None) }}"
    groups:
      - k8s_cluster

  calico_cni:
    enabled: "{{ kube_network_plugin == 'calico' }}"
    container: true
    repo: "{{ calico_cni_image_repo }}"
    tag: "{{ calico_cni_image_tag }}"
    sha256: "{{ calico_cni_digest_checksum | default(None) }}"
    groups:
      - k8s_cluster

  calico_flexvol:
    enabled: "{{ kube_network_plugin == 'calico' }}"
    container: true
    repo: "{{ calico_flexvol_image_repo }}"
    tag: "{{ calico_flexvol_image_tag }}"
    sha256: "{{ calico_flexvol_digest_checksum | default(None) }}"
    groups:
      - k8s_cluster

  calico_policy:
    enabled: "{{ enable_network_policy and kube_network_plugin in ['calico'] }}"
    container: true
    repo: "{{ calico_policy_image_repo }}"
    tag: "{{ calico_policy_image_tag }}"
    sha256: "{{ calico_policy_digest_checksum | default(None) }}"
    groups:
      - k8s_cluster

  calico_typha:
    enabled: "{{ typha_enabled }}"
    container: true
    repo: "{{ calico_typha_image_repo }}"
    tag: "{{ calico_typha_image_tag }}"
    sha256: "{{ calico_typha_digest_checksum | default(None) }}"
    groups:
      - k8s_cluster

  calico_apiserver:
    enabled: "{{ calico_apiserver_enabled }}"
    container: true
    repo: "{{ calico_apiserver_image_repo }}"
    tag: "{{ calico_apiserver_image_tag }}"
    sha256: "{{ calico_apiserver_digest_checksum | default(None) }}"
    groups:
      - k8s_cluster

  calico_crds:
    file: true
    enabled: "{{ kube_network_plugin == 'calico' and calico_datastore == 'kdd' }}"
    version: "{{ calico_version }}"
    dest: "{{ local_release_dir }}/calico-{{ calico_version }}-kdd-crds/{{ calico_version }}.tar.gz"
    sha256: "{{ calico_crds_archive_checksum }}"
    url: "{{ calico_crds_download_url }}"
    unarchive: true
    unarchive_extra_opts:
      - "{{ '--strip=6' if (calico_version is version('v3.22.3', '<')) else '--strip=3' }}"
      - "--wildcards"
      - "{{ '*/_includes/charts/calico/crds/kdd/' if (calico_version is version('v3.22.3', '<')) else '*/libcalico-go/config/crd/' }}"
    owner: "root"
    mode: "0755"
    groups:
      - kube_control_plane

  weave_kube:
    enabled: "{{ kube_network_plugin == 'weave' }}"
    container: true
    repo: "{{ weave_kube_image_repo }}"
    tag: "{{ weave_kube_image_tag }}"
    sha256: "{{ weave_kube_digest_checksum | default(None) }}"
    groups:
      - k8s_cluster

  weave_npc:
    enabled: "{{ kube_network_plugin == 'weave' }}"
    container: true
    repo: "{{ weave_npc_image_repo }}"
    tag: "{{ weave_npc_image_tag }}"
    sha256: "{{ weave_npc_digest_checksum | default(None) }}"
    groups:
      - k8s_cluster

  kube_ovn:
    enabled: "{{ kube_network_plugin == 'kube-ovn' }}"
    container: true
    repo: "{{ kube_ovn_container_image_repo }}"
    tag: "{{ kube_ovn_container_image_tag }}"
    sha256: "{{ kube_ovn_digest_checksum | default(None) }}"
    groups:
      - k8s_cluster

  kube_router:
    enabled: "{{ kube_network_plugin == 'kube-router' }}"
    container: true
    repo: "{{ kube_router_image_repo }}"
    tag: "{{ kube_router_image_tag }}"
    sha256: "{{ kube_router_digest_checksum | default(None) }}"
    groups:
      - k8s_cluster

  pod_infra:
    enabled: true
    container: true
    repo: "{{ pod_infra_image_repo }}"
    tag: "{{ pod_infra_image_tag }}"
    sha256: "{{ pod_infra_digest_checksum | default(None) }}"
    groups:
      - k8s_cluster

  kube-vip:
    enabled: "{{ kube_vip_enabled }}"
    container: true
    repo: "{{ kube_vip_image_repo }}"
    tag: "{{ kube_vip_image_tag }}"
    sha256: "{{ kube_vip_digest_checksum | default(None) }}"
    groups:
      - kube_control_plane

  nginx:
    enabled: "{{ loadbalancer_apiserver_localhost and loadbalancer_apiserver_type == 'nginx' }}"
    container: true
    repo: "{{ nginx_image_repo }}"
    tag: "{{ nginx_image_tag }}"
    sha256: "{{ nginx_digest_checksum | default(None) }}"
    groups:
      - kube_node

  haproxy:
    enabled: "{{ loadbalancer_apiserver_localhost and loadbalancer_apiserver_type == 'haproxy' }}"
    container: true
    repo: "{{ haproxy_image_repo }}"
    tag: "{{ haproxy_image_tag }}"
    sha256: "{{ haproxy_digest_checksum | default(None) }}"
    groups:
      - kube_node

  coredns:
    enabled: "{{ dns_mode in ['coredns', 'coredns_dual'] }}"
    container: true
    repo: "{{ coredns_image_repo }}"
    tag: "{{ coredns_image_tag }}"
    sha256: "{{ coredns_digest_checksum | default(None) }}"
    groups:
      - k8s_cluster

  nodelocaldns:
    enabled: "{{ enable_nodelocaldns }}"
    container: true
    repo: "{{ nodelocaldns_image_repo }}"
    tag: "{{ nodelocaldns_image_tag }}"
    sha256: "{{ nodelocaldns_digest_checksum | default(None) }}"
    groups:
      - k8s_cluster

  dnsautoscaler:
    enabled: "{{ dns_mode in ['coredns', 'coredns_dual'] }}"
    container: true
    repo: "{{ dnsautoscaler_image_repo }}"
    tag: "{{ dnsautoscaler_image_tag }}"
    sha256: "{{ dnsautoscaler_digest_checksum | default(None) }}"
    groups:
      - kube_control_plane

  helm:
    enabled: "{{ helm_enabled }}"
    file: true
    version: "{{ helm_version }}"
    dest: "{{ local_release_dir }}/helm-{{ helm_version }}/helm-{{ helm_version }}-linux-{{ image_arch }}.tar.gz"
    sha256: "{{ helm_archive_checksum }}"
    url: "{{ helm_download_url }}"
    unarchive: true
    owner: "root"
    mode: "0755"
    groups:
      - kube_control_plane

  krew:
    enabled: "{{ krew_enabled }}"
    file: true
    version: "{{ krew_version }}"
    dest: "{{ local_release_dir }}/krew-{{ host_os }}_{{ image_arch }}.tar.gz"
    sha256: "{{ krew_archive_checksum }}"
    url: "{{ krew_download_url }}"
    unarchive: true
    owner: "root"
    mode: "0755"
    groups:
      - kube_control_plane

  registry:
    enabled: "{{ registry_enabled }}"
    container: true
    repo: "{{ registry_image_repo }}"
    tag: "{{ registry_image_tag }}"
    sha256: "{{ registry_digest_checksum | default(None) }}"
    groups:
      - kube_node

  metrics_server:
    enabled: "{{ metrics_server_enabled }}"
    container: true
    repo: "{{ metrics_server_image_repo }}"
    tag: "{{ metrics_server_image_tag }}"
    sha256: "{{ metrics_server_digest_checksum | default(None) }}"
    groups:
      - kube_control_plane

  local_volume_provisioner:
    enabled: "{{ local_volume_provisioner_enabled }}"
    container: true
    repo: "{{ local_volume_provisioner_image_repo }}"
    tag: "{{ local_volume_provisioner_image_tag }}"
    sha256: "{{ local_volume_provisioner_digest_checksum | default(None) }}"
    groups:
      - kube_node

  cephfs_provisioner:
    enabled: "{{ cephfs_provisioner_enabled }}"
    container: true
    repo: "{{ cephfs_provisioner_image_repo }}"
    tag: "{{ cephfs_provisioner_image_tag }}"
    sha256: "{{ cephfs_provisioner_digest_checksum | default(None) }}"
    groups:
      - kube_node

  rbd_provisioner:
    enabled: "{{ rbd_provisioner_enabled }}"
    container: true
    repo: "{{ rbd_provisioner_image_repo }}"
    tag: "{{ rbd_provisioner_image_tag }}"
    sha256: "{{ rbd_provisioner_digest_checksum | default(None) }}"
    groups:
      - kube_node

  local_path_provisioner:
    enabled: "{{ local_path_provisioner_enabled }}"
    container: true
    repo: "{{ local_path_provisioner_image_repo }}"
    tag: "{{ local_path_provisioner_image_tag }}"
    sha256: "{{ local_path_provisioner_digest_checksum | default(None) }}"
    groups:
      - kube_node

  ingress_nginx_controller:
    enabled: "{{ ingress_nginx_enabled }}"
    container: true
    repo: "{{ ingress_nginx_controller_image_repo }}"
    tag: "{{ ingress_nginx_controller_image_tag }}"
    sha256: "{{ ingress_nginx_controller_digest_checksum | default(None) }}"
    groups:
      - kube_node

  ingress_alb_controller:
    enabled: "{{ ingress_alb_enabled }}"
    container: true
    repo: "{{ alb_ingress_image_repo }}"
    tag: "{{ alb_ingress_image_tag }}"
    sha256: "{{ ingress_alb_controller_digest_checksum | default(None) }}"
    groups:
      - kube_node

  cert_manager_controller:
    enabled: "{{ cert_manager_enabled }}"
    container: true
    repo: "{{ cert_manager_controller_image_repo }}"
    tag: "{{ cert_manager_controller_image_tag }}"
    sha256: "{{ cert_manager_controller_digest_checksum | default(None) }}"
    groups:
      - kube_node

  cert_manager_cainjector:
    enabled: "{{ cert_manager_enabled }}"
    container: true
    repo: "{{ cert_manager_cainjector_image_repo }}"
    tag: "{{ cert_manager_cainjector_image_tag }}"
    sha256: "{{ cert_manager_cainjector_digest_checksum | default(None) }}"
    groups:
      - kube_node

  cert_manager_webhook:
    enabled: "{{ cert_manager_enabled }}"
    container: true
    repo: "{{ cert_manager_webhook_image_repo }}"
    tag: "{{ cert_manager_webhook_image_tag }}"
    sha256: "{{ cert_manager_webhook_digest_checksum | default(None) }}"
    groups:
      - kube_node

  csi_attacher:
    enabled: "{{ cinder_csi_enabled or aws_ebs_csi_enabled }}"
    container: true
    repo: "{{ csi_attacher_image_repo }}"
    tag: "{{ csi_attacher_image_tag }}"
    sha256: "{{ csi_attacher_digest_checksum | default(None) }}"
    groups:
      - kube_node

  csi_provisioner:
    enabled: "{{ cinder_csi_enabled or aws_ebs_csi_enabled }}"
    container: true
    repo: "{{ csi_provisioner_image_repo }}"
    tag: "{{ csi_provisioner_image_tag }}"
    sha256: "{{ csi_provisioner_digest_checksum | default(None) }}"
    groups:
      - kube_node

  csi_snapshotter:
    enabled: "{{ cinder_csi_enabled or aws_ebs_csi_enabled }}"
    container: true
    repo: "{{ csi_snapshotter_image_repo }}"
    tag: "{{ csi_snapshotter_image_tag }}"
    sha256: "{{ csi_snapshotter_digest_checksum | default(None) }}"
    groups:
      - kube_node

  snapshot_controller:
    enabled: "{{ csi_snapshot_controller_enabled }}"
    container: true
    repo: "{{ snapshot_controller_image_repo }}"
    tag: "{{ snapshot_controller_image_tag }}"
    sha256: "{{ snapshot_controller_digest_checksum | default(None) }}"
    groups:
      - kube_node

  csi_resizer:
    enabled: "{{ cinder_csi_enabled or aws_ebs_csi_enabled }}"
    container: true
    repo: "{{ csi_resizer_image_repo }}"
    tag: "{{ csi_resizer_image_tag }}"
    sha256: "{{ csi_resizer_digest_checksum | default(None) }}"
    groups:
      - kube_node

  csi_node_driver_registrar:
    enabled: "{{ cinder_csi_enabled or aws_ebs_csi_enabled }}"
    container: true
    repo: "{{ csi_node_driver_registrar_image_repo }}"
    tag: "{{ csi_node_driver_registrar_image_tag }}"
    sha256: "{{ csi_node_driver_registrar_digest_checksum | default(None) }}"
    groups:
      - kube_node

  cinder_csi_plugin:
    enabled: "{{ cinder_csi_enabled }}"
    container: true
    repo: "{{ cinder_csi_plugin_image_repo }}"
    tag: "{{ cinder_csi_plugin_image_tag }}"
    sha256: "{{ cinder_csi_plugin_digest_checksum | default(None) }}"
    groups:
      - kube_node

  aws_ebs_csi_plugin:
    enabled: "{{ aws_ebs_csi_enabled }}"
    container: true
    repo: "{{ aws_ebs_csi_plugin_image_repo }}"
    tag: "{{ aws_ebs_csi_plugin_image_tag }}"
    sha256: "{{ aws_ebs_csi_plugin_digest_checksum | default(None) }}"
    groups:
      - kube_node

  dashboard:
    enabled: "{{ dashboard_enabled }}"
    container: true
    repo: "{{ dashboard_image_repo }}"
    tag: "{{ dashboard_image_tag }}"
    sha256: "{{ dashboard_digest_checksum | default(None) }}"
    groups:
      - kube_control_plane

  dashboard_metrics_scrapper:
    enabled: "{{ dashboard_enabled }}"
    container: true
    repo: "{{ dashboard_metrics_scraper_repo }}"
    tag: "{{ dashboard_metrics_scraper_tag }}"
    sha256: "{{ dashboard_digest_checksum | default(None) }}"
    groups:
      - kube_control_plane

  metallb_speaker:
    enabled: "{{ metallb_speaker_enabled }}"
    container: true
    repo: "{{ metallb_speaker_image_repo }}"
    tag: "{{ metallb_version }}"
    sha256: "{{ metallb_speaker_digest_checksum | default(None) }}"
    groups:
      - kube_control_plane

  metallb_controller:
    enabled: "{{ metallb_enabled }}"
    container: true
    repo: "{{ metallb_controller_image_repo }}"
    tag: "{{ metallb_version }}"
    sha256: "{{ metallb_controller_digest_checksum | default(None) }}"
    groups:
      - kube_control_plane

  yq:
    enabled: "{{ argocd_enabled }}"
    file: true
    version: "{{ yq_version }}"
    dest: "{{ local_release_dir }}/yq-{{ yq_version }}-{{ image_arch }}"
    sha256: "{{ yq_binary_checksum | default(None) }}"
    url: "{{ yq_download_url }}"
    unarchive: false
    owner: "root"
    mode: "0755"
    groups:
      - kube_control_plane

download_defaults:
  container: false
  file: false
  repo: None
  tag: None
  enabled: false
  dest: None
  version: None
  url: None
  unarchive: false
  owner: "{{ kube_owner }}"
  mode: None
