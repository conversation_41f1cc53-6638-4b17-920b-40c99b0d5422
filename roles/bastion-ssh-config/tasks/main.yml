---
- name: Set bastion host IP and port
  set_fact:
    bastion_ip: "{{ hostvars[groups['bastion'][0]]['ansible_host'] | d(hostvars[groups['bastion'][0]]['ansible_ssh_host']) }}"
    bastion_port: "{{ hostvars[groups['bastion'][0]]['ansible_port'] | d(hostvars[groups['bastion'][0]]['ansible_ssh_port']) | d(22) }}"
  delegate_to: localhost
  connection: local

# As we are actually running on localhost, the ansible_ssh_user is your local user when you try to use it directly
# To figure out the real ssh user, we delegate this task to the bastion and store the ansible_user in real_user
- name: Store the current ansible_user in the real_user fact
  set_fact:
    real_user: "{{ ansible_user }}"

- name: Create ssh bastion conf
  become: false
  delegate_to: localhost
  connection: local
  template:
    src: "{{ ssh_bastion_confing__name }}.j2"
    dest: "{{ playbook_dir }}/{{ ssh_bastion_confing__name }}"
    mode: 0640
