---
- name: Check ansible version
  import_playbook: ansible_version.yml

# These are inventory compatibility tasks to ensure we keep compatibility with old style group names

- name: Add kube-master nodes to kube_control_plane
  hosts: kube-master
  gather_facts: false
  tags: always
  tasks:
    - name: Add nodes to kube_control_plane group
      group_by:
        key: 'kube_control_plane'

- name: Add kube-node nodes to kube_node
  hosts: kube-node
  gather_facts: false
  tags: always
  tasks:
    - name: Add nodes to kube_node group
      group_by:
        key: 'kube_node'

- name: Add k8s-cluster nodes to k8s_cluster
  hosts: k8s-cluster
  gather_facts: false
  tags: always
  tasks:
    - name: Add nodes to k8s_cluster group
      group_by:
        key: 'k8s_cluster'

- name: Add calico-rr nodes to calico_rr
  hosts: calico-rr
  gather_facts: false
  tags: always
  tasks:
    - name: Add nodes to calico_rr group
      group_by:
        key: 'calico_rr'

- name: Add no-floating nodes to no_floating
  hosts: no-floating
  gather_facts: false
  tags: always
  tasks:
    - name: Add nodes to no-floating group
      group_by:
        key: 'no_floating'

- name: Install bastion ssh config
  hosts: bastion[0]
  gather_facts: False
  environment: "{{ proxy_disable_env }}"
  roles:
    - { role: kubespray-defaults }
    - { role: bastion-ssh-config, tags: ["localhost", "bastion"] }
