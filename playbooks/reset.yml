---
- name: Common tasks for every playbooks
  import_playbook: boilerplate.yml

- name: Gather facts
  import_playbook: facts.yml

- name: Reset cluster
  hosts: etcd:k8s_cluster:calico_rr
  gather_facts: False
  pre_tasks:
    - name: Reset Confirmation
      pause:
        prompt: "Are you sure you want to reset cluster state? Type 'yes' to reset your cluster."
      register: reset_confirmation_prompt
      run_once: True
      when:
        - not (skip_confirmation | default(false) | bool)
        - reset_confirmation is not defined

    - name: Check confirmation
      fail:
        msg: "Reset confirmation failed"
      when:
        - not reset_confirmation | default(false) | bool
        - not reset_confirmation_prompt.user_input | default("") == "yes"

    - name: Gather information about installed services
      service_facts:

  environment: "{{ proxy_disable_env }}"
  roles:
    - { role: kubespray-defaults}
    - { role: kubernetes/preinstall, when: "dns_mode != 'none' and resolvconf_mode == 'host_resolvconf'", tags: resolvconf, dns_early: true }
    - { role: reset, tags: reset }
