---
- name: Common tasks for every playbooks
  import_playbook: boilerplate.yml

- name: Gather facts
  import_playbook: facts.yml

- name: Download images to ansible host cache via first kube_control_plane node
  hosts: kube_control_plane[0]
  gather_facts: False
  any_errors_fatal: "{{ any_errors_fatal | default(true) }}"
  environment: "{{ proxy_disable_env }}"
  roles:
    - { role: kubespray-defaults, when: "not skip_downloads and download_run_once and not download_localhost"}
    - { role: kubernetes/preinstall, tags: preinstall, when: "not skip_downloads and download_run_once and not download_localhost" }
    - { role: download, tags: download, when: "not skip_downloads and download_run_once and not download_localhost" }

- name: Prepare nodes for upgrade
  hosts: k8s_cluster:etcd:calico_rr
  gather_facts: False
  any_errors_fatal: "{{ any_errors_fatal | default(true) }}"
  environment: "{{ proxy_disable_env }}"
  roles:
    - { role: kubespray-defaults }
    - { role: kubernetes/preinstall, tags: preinstall }
    - { role: download, tags: download, when: "not skip_downloads" }

- name: Upgrade container engine on non-cluster nodes
  hosts: etcd:calico_rr:!k8s_cluster
  gather_facts: False
  any_errors_fatal: "{{ any_errors_fatal | default(true) }}"
  environment: "{{ proxy_disable_env }}"
  serial: "{{ serial | default('20%') }}"
  roles:
    - { role: kubespray-defaults }
    - { role: container-engine, tags: "container-engine", when: deploy_container_engine }

- name: Install etcd
  import_playbook: install_etcd.yml

- name: Handle upgrades to master components first to maintain backwards compat.
  gather_facts: False
  hosts: kube_control_plane
  any_errors_fatal: "{{ any_errors_fatal | default(true) }}"
  environment: "{{ proxy_disable_env }}"
  serial: 1
  roles:
    - { role: kubespray-defaults }
    - { role: upgrade/pre-upgrade, tags: pre-upgrade }
    - { role: upgrade/system-upgrade, tags: system-upgrade }
    - { role: download, tags: download, when: "system_upgrade and system_upgrade_reboot != 'never' and not skip_downloads" }
    - { role: kubernetes-apps/kubelet-csr-approver, tags: kubelet-csr-approver }
    - { role: container-engine, tags: "container-engine", when: deploy_container_engine }
    - { role: kubernetes/node, tags: node }
    - { role: kubernetes/control-plane, tags: master, upgrade_cluster_setup: true }
    - { role: kubernetes/client, tags: client }
    - { role: kubernetes/node-label, tags: node-label }
    - { role: kubernetes/node-taint, tags: node-taint }
    - { role: kubernetes-apps/cluster_roles, tags: cluster-roles }
    - { role: kubernetes-apps, tags: csi-driver }
    - { role: upgrade/post-upgrade, tags: post-upgrade }

- name: Upgrade calico and external cloud provider on all masters, calico-rrs, and nodes
  hosts: kube_control_plane:calico_rr:kube_node
  gather_facts: False
  any_errors_fatal: "{{ any_errors_fatal | default(true) }}"
  serial: "{{ serial | default('20%') }}"
  environment: "{{ proxy_disable_env }}"
  roles:
    - { role: kubespray-defaults }
    - { role: kubernetes-apps/external_cloud_controller, tags: external-cloud-controller }
    - { role: network_plugin, tags: network }
    - { role: kubernetes-apps/network_plugin, tags: network }
    - { role: kubernetes-apps/policy_controller, tags: policy-controller }

- name: Finally handle worker upgrades, based on given batch size
  hosts: kube_node:calico_rr:!kube_control_plane
  gather_facts: False
  any_errors_fatal: "{{ any_errors_fatal | default(true) }}"
  environment: "{{ proxy_disable_env }}"
  serial: "{{ serial | default('20%') }}"
  roles:
    - { role: kubespray-defaults }
    - { role: upgrade/pre-upgrade, tags: pre-upgrade }
    - { role: upgrade/system-upgrade, tags: system-upgrade }
    - { role: download, tags: download, when: "system_upgrade and system_upgrade_reboot != 'never' and not skip_downloads" }
    - { role: container-engine, tags: "container-engine", when: deploy_container_engine }
    - { role: kubernetes/node, tags: node }
    - { role: kubernetes/kubeadm, tags: kubeadm }
    - { role: kubernetes/node-label, tags: node-label }
    - { role: kubernetes/node-taint, tags: node-taint }
    - { role: upgrade/post-upgrade, tags: post-upgrade }

- name: Patch Kubernetes for Windows
  hosts: kube_control_plane[0]
  gather_facts: False
  any_errors_fatal: true
  environment: "{{ proxy_disable_env }}"
  roles:
    - { role: kubespray-defaults }
    - { role: win_nodes/kubernetes_patch, tags: ["master", "win_nodes"] }

- name: Install Calico Route Reflector
  hosts: calico_rr
  gather_facts: False
  any_errors_fatal: "{{ any_errors_fatal | default(true) }}"
  environment: "{{ proxy_disable_env }}"
  roles:
    - { role: kubespray-defaults }
    - { role: network_plugin/calico/rr, tags: network }

- name: Install Kubernetes apps
  hosts: kube_control_plane
  gather_facts: False
  any_errors_fatal: "{{ any_errors_fatal | default(true) }}"
  environment: "{{ proxy_disable_env }}"
  roles:
    - { role: kubespray-defaults }
    - { role: kubernetes-apps/ingress_controller, tags: ingress-controller }
    - { role: kubernetes-apps/external_provisioner, tags: external-provisioner }
    - { role: kubernetes-apps, tags: apps }

- name: Apply resolv.conf changes now that cluster DNS is up
  hosts: k8s_cluster
  gather_facts: False
  any_errors_fatal: "{{ any_errors_fatal | default(true) }}"
  environment: "{{ proxy_disable_env }}"
  roles:
    - { role: kubespray-defaults }
    - { role: kubernetes/preinstall, when: "dns_mode != 'none' and resolvconf_mode == 'host_resolvconf'", tags: resolvconf, dns_late: true }
