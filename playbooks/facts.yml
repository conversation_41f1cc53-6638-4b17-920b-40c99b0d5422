---
- name: Bootstrap hosts for Ansible
  hosts: k8s_cluster:etcd:calico_rr
  strategy: linear
  any_errors_fatal: "{{ any_errors_fatal | default(true) }}"
  gather_facts: false
  environment: "{{ proxy_disable_env }}"
  vars:
    # Need to disable pipelining for bootstrap-os as some systems have requiretty in sudoers set, which makes pipelining
    # fail. bootstrap-os fixes this on these systems, so in later plays it can be enabled.
    ansible_ssh_pipelining: false
  roles:
    - { role: kubespray-defaults }
    - { role: bootstrap-os, tags: bootstrap-os}

- name: Gather facts
  hosts: k8s_cluster:etcd:calico_rr
  gather_facts: False
  tags: always
  tasks:
    - name: Gather minimal facts
      setup:
        gather_subset: '!all'

    # filter match the following variables:
    # ansible_default_ipv4
    # ansible_default_ipv6
    # ansible_all_ipv4_addresses
    # ansible_all_ipv6_addresses
    - name: Gather necessary facts (network)
      setup:
        gather_subset: '!all,!min,network'
        filter: "ansible_*_ipv[46]*"

    # filter match the following variables:
    # ansible_memtotal_mb
    # ansible_swaptotal_mb
    - name: Gather necessary facts (hardware)
      setup:
        gather_subset: '!all,!min,hardware'
        filter: "ansible_*total_mb"
