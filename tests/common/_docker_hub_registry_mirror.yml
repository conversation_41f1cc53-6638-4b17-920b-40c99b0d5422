---
docker_registry_mirrors:
  - "https://mirror.gcr.io"

containerd_grpc_max_recv_message_size: 16777216
containerd_grpc_max_send_message_size: 16777216

containerd_registries_mirrors:
  - prefix: docker.io
    mirrors:
      - host: https://mirror.gcr.io
        capabilities: ["pull", "resolve"]
        skip_verify: false
      - host: https://registry-1.docker.io
        capabilities: ["pull", "resolve"]
        skip_verify: false

containerd_max_container_log_line_size: -1

crio_registries:
  - prefix: docker.io
    insecure: false
    blocked: false
    unqualified: false
    location: registry-1.docker.io
    mirrors:
      - location: mirror.gcr.io
        insecure: false

netcheck_agent_image_repo: "{{ quay_image_repo }}/kubespray/k8s-netchecker-agent"
netcheck_server_image_repo: "{{ quay_image_repo }}/kubespray/k8s-netchecker-server"

nginx_image_repo: "{{ quay_image_repo }}/kubespray/nginx"

flannel_image_repo: "{{ quay_image_repo }}/kubespray/flannel"
flannel_init_image_repo: "{{ quay_image_repo }}/kubespray/flannel-cni-plugin"

# Kubespray settings for tests
deploy_netchecker: true
dns_min_replicas: 1
