---
- name: Testcases checking pods
  hosts: kube_control_plane[0]
  tasks:

  - name: Force binaries directory for Flatcar Container Linux by Kinvolk
    set_fact:
      bin_dir: "/opt/bin"
    when: ansible_os_family in ["Flatcar", "Flatcar Container Linux by Kinvolk"]

  - name: Force binaries directory for other hosts
    set_fact:
      bin_dir: "/usr/local/bin"
    when: not ansible_os_family in ["Flatcar", "Flatcar Container Linux by Kinvolk"]

  - import_role:  # noqa name[missing]
      name: cluster-dump

  - name: Check kubectl output
    command: "{{ bin_dir }}/kubectl get pods --all-namespaces -owide"
    changed_when: false
    register: get_pods
    no_log: true

  - debug:  # noqa name[missing]
      msg: "{{ get_pods.stdout.split('\n') }}"

  - name: Check that all pods are running and ready
    command: "{{ bin_dir }}/kubectl get pods --all-namespaces --no-headers -o yaml"
    changed_when: false
    register: run_pods_log
    until:
    # Check that all pods are running
    - '(run_pods_log.stdout | from_yaml)["items"] | map(attribute = "status.phase") | unique | list == ["Running"]'
    # Check that all pods are ready
    - '(run_pods_log.stdout | from_yaml)["items"] | map(attribute = "status.containerStatuses") | map("map", attribute = "ready") | map("min") | min'
    retries: 30
    delay: 10
    failed_when: false
    no_log: true

  - name: Check kubectl output
    command: "{{ bin_dir }}/kubectl get pods --all-namespaces -owide"
    changed_when: false
    register: get_pods
    no_log: true

  - debug:  # noqa name[missing]
      msg: "{{ get_pods.stdout.split('\n') }}"
    failed_when: not run_pods_log is success
