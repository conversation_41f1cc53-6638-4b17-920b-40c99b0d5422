---
- name: Testcases for kubernetes conformance
  hosts: kube_control_plane[0]
  vars:
    sonobuoy_version: 0.56.11
    sonobuoy_arch: amd64
    sonobuoy_parallel: 30
    sonobuoy_path: /usr/local/bin/sonobuoy
    sonobuoy_mode: Quick

  tasks:
  - name: Run sonobuoy
    when:
    - sonobuoy_enabled is defined
    - sonobuoy_enabled
    block:
    - name: Download sonobuoy
      get_url:
        url: "https://github.com/heptio/sonobuoy/releases/download/v{{ sonobuoy_version }}/sonobuoy_{{ sonobuoy_version }}_linux_{{ sonobuoy_arch }}.tar.gz"
        dest: /tmp/sonobuoy.tar.gz
        mode: 0644

    - name: Extract sonobuoy
      unarchive:
        src: /tmp/sonobuoy.tar.gz
        dest: /usr/local/bin/
        copy: no

    - name: Run sonobuoy
      command: "{{ sonobuoy_path }} run --mode {{ sonobuoy_mode }} --e2e-parallel {{ sonobuoy_parallel }} --wait"
      when: sonobuoy_enabled | default(false)

    - name: Run sonobuoy retrieve
      command: "{{ sonobuoy_path }} retrieve"
      register: sonobuoy_retrieve

    - name: Run inspect results
      command: "{{ sonobuoy_path }} results {{ sonobuoy_retrieve.stdout }} --plugin e2e --mode report"
