---
- name: Generate dump folder
  command: "{{ bin_dir }}/kubectl cluster-info dump --all-namespaces --output-directory /tmp/cluster-dump"
  no_log: true
  when: inventory_hostname in groups['kube_control_plane']

- name: Compress directory cluster-dump
  community.general.archive:
    path: /tmp/cluster-dump
    dest: /tmp/cluster-dump.tgz
    mode: 0644
  when: inventory_hostname in groups['kube_control_plane']

- name: Fetch dump file
  fetch:
    src: /tmp/cluster-dump.tgz
    dest: "{{ lookup('env', 'CI_PROJECT_DIR') }}/cluster-dump/{{ inventory_hostname }}.tgz"
    flat: true
  when: inventory_hostname in groups['kube_control_plane']
