---
# Instance settings
cloud_image: ubuntu-2204
mode: all-in-one
vm_memory: 1600Mi

# Kubespray settings
auto_renew_certificates: true

# Currently ipvs not available on KVM: https://packages.ubuntu.com/search?suite=focal&arch=amd64&mode=exactfilename&searchon=contents&keywords=ip_vs_sh.ko
kube_proxy_mode: iptables
enable_nodelocaldns: False

containerd_registries_mirrors:
  - prefix: docker.io
    mirrors:
      - host: https://mirror.gcr.io
        capabilities: ["pull", "resolve"]
        skip_verify: false
  - prefix: ************:5000
    mirrors:
      - host: http://************:5000
        capabilities: ["pull", "resolve", "push"]
        skip_verify: true
