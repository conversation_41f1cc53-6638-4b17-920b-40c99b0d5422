---
# Instance settings
cloud_image: ubuntu-2004
mode: ha

# use the kubeadm etcd setting to test the upgrade
etcd_deployment_type: kubeadm

upgrade_cluster_setup: true

# Currently ipvs not available on KVM: https://packages.ubuntu.com/search?suite=focal&arch=amd64&mode=exactfilename&searchon=contents&keywords=ip_vs_sh.ko
kube_proxy_mode: iptables
enable_nodelocaldns: False

# Pin disabling ipip mode to ensure proper upgrade
ipip: false
calico_vxlan_mode: Always
calico_network_backend: bird

# Needed to bypass deprecation check
ignore_assert_errors: true
### FIXME FLORYUT Needed for upgrade job, will be removed when releasing kubespray 2.20
calico_pool_blocksize: 24
### /FIXME
