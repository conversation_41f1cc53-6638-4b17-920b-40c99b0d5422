---
# Instance settings
cloud_image: debian-12
mode: default

# Kubespray settings
kube_owner: root
kube_network_plugin: custom_cni
custom_cni_chart_namespace: kube-system
custom_cni_chart_release_name: cilium
custom_cni_chart_repository_name: cilium
custom_cni_chart_repository_url: https://helm.cilium.io
custom_cni_chart_ref: cilium/cilium
custom_cni_chart_version: 1.14.3
custom_cni_chart_values:
  cluster:
    name: kubespray
  hubble:
    enabled: false
  ipam:
    operator:
      clusterPoolIPv4PodCIDRList:
        - "{{ kube_pods_subnet }}"
