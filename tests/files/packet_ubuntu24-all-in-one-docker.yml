---
# Instance settings
cloud_image: ubuntu-2404
mode: all-in-one
vm_memory: 1600Mi

# Kubespray settings
auto_renew_certificates: true

# Currently ipvs not available on KVM: https://packages.ubuntu.com/search?suite=noble&arch=amd64&mode=exactfilename&searchon=contents&keywords=ip_vs_sh.ko
kube_proxy_mode: iptables
enable_nodelocaldns: False

# Use docker
container_manager: docker
etcd_deployment_type: docker
resolvconf_mode: docker_dns
docker_repo_key_keyring: /etc/apt/trusted.gpg.d/docker.gpg
