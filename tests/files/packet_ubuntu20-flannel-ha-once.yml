---
# Instance settings
cloud_image: ubuntu-2004
mode: ha

# Kubespray settings
kubeadm_certificate_key: 3998c58db6497dd17d909394e62d515368c06ec617710d02edea31c06d741085
kube_proxy_mode: iptables
kube_network_plugin: flannel
helm_enabled: true
krew_enabled: true
kubernetes_audit: true
etcd_events_cluster_enabled: true
local_volume_provisioner_enabled: true
kube_encrypt_secret_data: true
ingress_nginx_enabled: true
cert_manager_enabled: true
# Disable as health checks are still unstable and slow to respond.
metrics_server_enabled: false
metrics_server_kubelet_insecure_tls: true
kube_token_auth: true
enable_nodelocaldns: false
