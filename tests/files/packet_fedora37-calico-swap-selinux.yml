---
# Instance settings
cloud_image: fedora-37
mode: default

# Kubespray settings
auto_renew_certificates: true
# Switching to iptable due to https://github.com/projectcalico/calico/issues/5011
# Kubernetes v1.23.0 kube-proxy does use v.7.x now. Calico v3.20.x/v3.21.x Pods show the following error
# Bad return code from 'ipset list'. error=exit status 1 family="inet" stderr="ipset v7.1: Kernel and userspace incompatible: settype hash:ip,port with revision 6 not supported by userspace.
kube_proxy_mode: iptables

# Test with SELinux in enforcing mode
preinstall_selinux_state: enforcing

# Test Alpha swap feature by leveraging zswap default config in Fedora 35
kubelet_fail_swap_on: false
kube_feature_gates:
  - "NodeSwap=True"
