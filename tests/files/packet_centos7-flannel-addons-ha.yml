---
# Instance settings
cloud_image: centos-7
mode: ha

# Kubespray settings
kubeadm_certificate_key: 3998c58db6497dd17d909394e62d515368c06ec617710d02edea31c06d741085
kube_proxy_mode: iptables
kube_network_plugin: flannel
download_localhost: false
download_run_once: true
helm_enabled: true
krew_enabled: true
kubernetes_audit: true
etcd_events_cluster_enabled: true
local_volume_provisioner_enabled: true
kube_encrypt_secret_data: true
ingress_nginx_enabled: true
ingress_nginx_webhook_enabled: true
ingress_nginx_webhook_job_ttl: 30
cert_manager_enabled: true
# Disable as health checks are still unstable and slow to respond.
metrics_server_enabled: false
metrics_server_kubelet_insecure_tls: true
kube_token_auth: true
enable_nodelocaldns: false
kubelet_rotate_server_certificates: true
kubelet_csr_approver_enabled: false

kube_oidc_url: https://accounts.google.com/.well-known/openid-configuration
kube_oidc_client_id: kubespray-example

tls_min_version: "VersionTLS12"
tls_cipher_suites:
  - TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256

# test etcd tls cipher suites
etcd_tls_cipher_suites:
  - TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256
  - TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384

# Containerd
containerd_storage_dir: /var/data/containerd
containerd_state_dir: /run/cri/containerd
containerd_oom_score: -999

# Kube-vip
kube_vip_enabled: true
kube_vip_arp_enabled: true
kube_vip_controlplane_enabled: true
kube_vip_address: *************

# MetalLB
metallb_enabled: true
metallb_speaker_enabled: true
metallb_config:
  address_pools:
    primary:
      ip_range:
        - *********-***********
      auto_assign: true
    pool1:
      ip_range:
        - *********-*********
      auto_assign: false
    pool2:
      ip_range:
        - *********-*********
      auto_assign: false

  layer2:
    - primary
    - pool1
    - pool2
