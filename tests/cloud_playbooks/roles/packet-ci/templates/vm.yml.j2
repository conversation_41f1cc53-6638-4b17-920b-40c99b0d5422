---
apiVersion: kubevirt.io/v1alpha3
kind: VirtualMachine
metadata:
  name: "instance-{{ vm_id }}"
  namespace: "{{ test_name }}"
  labels:
    kubevirt.io/os: {{ cloud_image }}
spec:
  running: true
  template:
    metadata:
      labels:
        kubevirt.io/size: small
        kubevirt.io/domain: "{{ test_name }}"
    spec:
      domain:
        devices:
          blockMultiQueue: true
          disks:
            - disk:
                bus: virtio
              name: containervolume
              cache: writethrough
            - disk:
                bus: virtio
              name: cloudinitvolume
          interfaces:
          - name: default
            bridge: {}
        cpu:
            cores: {{ vm_cpu_cores }}
            sockets: {{ vm_cpu_sockets }}
            threads: {{ vm_cpu_threads }}
        resources:
          requests:
            memory: {{ vm_memory * memory_allocation_ratio }}
            cpu: {{ vm_cpu_cores * cpu_allocation_ratio }}
          limits:
            memory: {{ vm_memory }}
            cpu: {{ vm_cpu_cores }}
      networks:
      - name: default
        pod: {}
      terminationGracePeriodSeconds: 0
      volumes:
        - name: containervolume
          containerDisk:
            image: quay.io/kubespray/vm-{{ cloud_image }}
        - name: cloudinitvolume
          cloudInitNoCloud:
            userDataBase64: {{ cloud_init[cloud_image] }}
