[all]
{% for instance in vms.results %}
instance-{{ loop.index }} ansible_host={{instance.stdout}}
{% endfor %}

{% if mode is defined and mode in ["separate", "separate-scale"] %}
[kube_control_plane]
instance-1

[kube_node]
instance-2

[etcd]
instance-3
{% elif mode is defined and mode in ["ha", "ha-scale"] %}
[kube_control_plane]
instance-1
instance-2

[kube_node]
instance-3

[etcd]
instance-1
instance-2
instance-3
{% elif mode == "default" %}
[kube_control_plane]
instance-1

[kube_node]
instance-2

[etcd]
instance-1
{% elif mode == "all-in-one" %}
[kube_control_plane]
instance-1

[kube_node]
instance-1

[etcd]
instance-1
{% elif mode == "ha-recover" %}
[kube_control_plane]
instance-1
instance-2

[kube_node]
instance-3

[etcd]
instance-3
instance-1
instance-2

[broken_kube_control_plane]
instance-2

[broken_etcd]
instance-2 etcd_member_name=etcd3
{% elif mode == "ha-recover-noquorum" %}
[kube_control_plane]
instance-3
instance-1
instance-2

[kube_node]
instance-3

[etcd]
instance-3
instance-1
instance-2

[broken_kube_control_plane]
instance-1
instance-2

[broken_etcd]
instance-1 etcd_member_name=etcd2
instance-2 etcd_member_name=etcd3
{% elif mode == "node-etcd-client" %}
[kube_control_plane]
instance-1

[etcd]
instance-1
instance-2
instance-3

[kube_node]
instance-1
instance-2
instance-3
instance-4
{% endif %}

[k8s_cluster:children]
kube_node
kube_control_plane
calico_rr

[calico_rr]

[fake_hosts]
