---

- name: Fetch a list of namespaces
  kubernetes.core.k8s_info:
    api_version: v1
    kind: Namespace
    label_selectors:
      - cijobs = true
      - branch = {{ branch_name_sane }}
  register: namespaces

- name: Delete older namespaces
  command: "kubectl delete namespace {{ item.metadata.name }}"
  failed_when: false
  loop: "{{ namespaces.resources }}"
  when:
    - (item.metadata.labels.pipeline_id | int) < (pipeline_id | int)
