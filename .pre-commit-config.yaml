---
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v3.4.0
    hooks:
      - id: check-added-large-files
      - id: check-case-conflict
      - id: check-executables-have-shebangs
      - id: check-xml
      - id: check-merge-conflict
      - id: detect-private-key
      - id: end-of-file-fixer
      - id: forbid-new-submodules
      - id: requirements-txt-fixer
      - id: trailing-whitespace

  - repo: https://github.com/adrienverge/yamllint.git
    rev: v1.27.1
    hooks:
      - id: yamllint
        args: [--strict]

  - repo: https://github.com/markdownlint/markdownlint
    rev: v0.11.0
    hooks:
      - id: markdownlint
        args: [-r, "~MD013,~MD029"]
        exclude: "^.git"

  - repo: https://github.com/jumanjihouse/pre-commit-hooks
    rev: 3.0.0
    hooks:
      - id: shellcheck
        args: [--severity, "error"]
        exclude: "^.git"
        files: "\\.sh$"

  - repo: local
    hooks:
      - id: ansible-lint
        name: ansible-lint
        entry: ansible-lint -v
        language: python
        pass_filenames: false
        additional_dependencies:
          - .[community]

      - id: ansible-syntax-check
        name: ansible-syntax-check
        entry: env ANSIBLE_INVENTORY=inventory/local-tests.cfg ANSIBLE_REMOTE_USER=root ANSIBLE_BECOME="true" ANSIBLE_BECOME_USER=root ANSIBLE_VERBOSITY="3" ansible-playbook --syntax-check
        language: python
        files: "^cluster.yml|^upgrade-cluster.yml|^reset.yml|^extra_playbooks/upgrade-only-k8s.yml"

      - id: tox-inventory-builder
        name: tox-inventory-builder
        entry: bash -c "cd contrib/inventory_builder && tox"
        language: python
        pass_filenames: false

      - id: check-readme-versions
        name: check-readme-versions
        entry: tests/scripts/check_readme_versions.sh
        language: script
        pass_filenames: false

      - id: generate-docs-sidebar
        name: generate-docs-sidebar
        entry: scripts/gen_docs_sidebar.sh
        language: script
        pass_filenames: false

      - id: ci-matrix
        name: ci-matrix
        entry: tests/scripts/md-table/test.sh
        language: script
        pass_filenames: false

      - id: jinja-syntax-check
        name: jinja-syntax-check
        entry: tests/scripts/check-templates.py
        language: python
        types:
          - jinja
        additional_dependencies:
          - Jinja2
