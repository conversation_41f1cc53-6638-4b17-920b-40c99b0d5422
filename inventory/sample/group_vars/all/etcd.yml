---
## Directory where etcd data stored
etcd_data_dir: /var/lib/etcd

## Container runtime
## docker for docker, crio for cri-o and containerd for containerd.
## Additionally you can set this to kubeadm if you want to install etcd using kubeadm
## Kubeadm etcd deployment is experimental and only available for new deployments
## If this is not set, container manager will be inherited from the Kubespray defaults
## and not from k8s_cluster/k8s-cluster.yml, which might not be what you want.
## Also this makes possible to use different container manager for etcd nodes.
# container_manager: containerd

## Settings for etcd deployment type
# Set this to docker if you are using container_manager: docker
etcd_deployment_type: host
