all:
  hosts:
    testing-k8s-master-1:
      ansible_host: *************
      ip: *************
      access_ip: *************
    testing-k8s-master-2:
      ansible_host: *************
      ip: *************
      access_ip: *************
    testing-k8s-master-3:
      ansible_host: *************
      ip: *************
      access_ip: *************
    testing-k8s-01:
      ansible_host: *************
      ip: *************
      access_ip: *************
    testing-k8s-02:
      ansible_host: *************
      ip: *************
      access_ip: *************
    testing-k8s-03:
      ansible_host: *************
      ip: *************
      access_ip: *************
    testing-k8s-04:
      ansible_host: *************
      ip: *************
      access_ip: *************
    testing-k8s-05:
      ansible_host: *************
      ip: *************
      access_ip: *************
  children:
    kube_control_plane:
      hosts:
        testing-k8s-master-1:
        testing-k8s-master-2:
        testing-k8s-master-3:
    kube_node:
      hosts:
        testing-k8s-01:
        testing-k8s-02:
        testing-k8s-03:
        # testing-k8s-04:
        # testing-k8s-05:
    etcd:
      hosts:
        testing-k8s-master-1:
        testing-k8s-master-2:
        testing-k8s-master-3:
    k8s_cluster:
      children:
        kube_control_plane:
        kube_node:
