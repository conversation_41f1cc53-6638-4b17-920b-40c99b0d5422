## Values for the external Hcloud Cloud Controller
# external_hcloud_cloud:
#   hcloud_api_token: ""
#   token_secret_name: hcloud
#   with_networks: false # Use the hcloud controller-manager with networks support https://github.com/hetznercloud/hcloud-cloud-controller-manager#networks-support
#   network_name: # network name/ID: If you manage the network yourself it might still be required to let the CCM know about private networks
#   service_account_name: cloud-controller-manager
#
#   controller_image_tag: "latest"
#   ## A dictionary of extra arguments to add to the openstack cloud controller manager daemonset
#   ## Format:
#   ##  external_hcloud_cloud.controller_extra_args:
#   ##    arg1: "value1"
#   ##    arg2: "value2"
#   controller_extra_args: {}
#
#   load_balancers_location: # mutually exclusive with load_balancers_network_zone
#   load_balancers_network_zone:
#   load_balancers_disable_private_ingress: # set to true if using IPVS based plugins https://github.com/hetznercloud/hcloud-cloud-controller-manager/blob/main/docs/load_balancers.md#sample-service-with-networks
#   load_balancers_use_private_ip: # set to true if using private networks
#   load_balancers_enabled:
#   network_routes_enabled:
