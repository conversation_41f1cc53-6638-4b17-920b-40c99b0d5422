---
stages:
  - build
  - unit-tests
  - deploy-part1
  - moderator
  - deploy-part2
  - deploy-part3
  - deploy-special

variables:
  KUBESPRAY_VERSION: v2.24.1
  FAILFASTCI_NAMESPACE: 'kargo-ci'
  GITLAB_REPOSITORY: 'kargo-ci/kubernetes-sigs-kubespray'
  ANSIBLE_FORCE_COLOR: "true"
  MAGIC: "ci check this"
  TEST_ID: "$CI_PIPELINE_ID-$CI_JOB_ID"
  CI_TEST_VARS: "./tests/files/${CI_JOB_NAME}.yml"
  CI_TEST_REGISTRY_MIRROR: "./tests/common/_docker_hub_registry_mirror.yml"
  CI_TEST_SETTING: "./tests/common/_kubespray_test_settings.yml"
  GS_ACCESS_KEY_ID: $GS_KEY
  GS_SECRET_ACCESS_KEY: $GS_SECRET
  CONTAINER_ENGINE: docker
  SSH_USER: root
  GCE_PREEMPTIBLE: "false"
  ANSIBLE_KEEP_REMOTE_FILES: "1"
  ANSIBLE_CONFIG: ./tests/ansible.cfg
  ANSIBLE_INVENTORY: ./inventory/sample/${CI_JOB_NAME}-${BUILD_NUMBER}.ini
  IDEMPOT_CHECK: "false"
  RESET_CHECK: "false"
  REMOVE_NODE_CHECK: "false"
  UPGRADE_TEST: "false"
  MITOGEN_ENABLE: "false"
  ANSIBLE_LOG_LEVEL: "-vv"
  RECOVER_CONTROL_PLANE_TEST: "false"
  RECOVER_CONTROL_PLANE_TEST_GROUPS: "etcd[2:]:kube_control_plane[1:]"
  TERRAFORM_VERSION: 1.3.7
  PIPELINE_IMAGE: "$CI_REGISTRY_IMAGE/pipeline:${CI_PIPELINE_ID}-${CI_COMMIT_SHORT_SHA}"

before_script:
  - ./tests/scripts/rebase.sh
  - mkdir -p /.ssh

.job: &job
  tags:
    - packet
  image: $PIPELINE_IMAGE
  artifacts:
    when: always
    paths:
      - cluster-dump/

.testcases: &testcases
  <<: *job
  retry: 1
  interruptible: true
  before_script:
    - update-alternatives --install /usr/bin/python python /usr/bin/python3 1
    - ./tests/scripts/rebase.sh
    - ./tests/scripts/testcases_prepare.sh
  script:
    - ./tests/scripts/testcases_run.sh
  after_script:
    - chronic ./tests/scripts/testcases_cleanup.sh

# For failfast, at least 1 job must be defined in .gitlab-ci.yml
# Premoderated with manual actions
ci-authorized:
  extends: .job
  stage: moderator
  script:
    - /bin/sh scripts/premoderator.sh
  except: ['triggers', 'master']
  # Disable ci moderator
  only: []

include:
  - .gitlab-ci/build.yml
  - .gitlab-ci/lint.yml
  - .gitlab-ci/shellcheck.yml
  - .gitlab-ci/terraform.yml
  - .gitlab-ci/packet.yml
  - .gitlab-ci/vagrant.yml
  - .gitlab-ci/molecule.yml
